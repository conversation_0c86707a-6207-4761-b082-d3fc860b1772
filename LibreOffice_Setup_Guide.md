# ПОШАГОВОЕ РУКОВОДСТВО 
## Электронный журнал выпуска продукции в LibreOffice Base 
 
### ШАГ 1: Создание базы данных 
1. Откройте LibreOffice Base 
2. Выберите "Создать базу данных" 
3. Выберите "Встроенная база данных HSQLDB" 
4. Сохраните как "Журнал_выпуска_продукции.odb" 
 
### ШАГ 2: Создание таблиц 
1. Перейдите в режим SQL (Инструменты > SQL) 
2. Скопируйте содержимое файла create_tables.sql 
3. Выполните команды по очереди 
 
### ШАГ 3: Создание форм 
1. Создайте форму на основе таблицы ЖУРНАЛ_ВЫПУСКА 
2. Добавьте комбо-бокс для выбора материала 
3. Настройте автозаполнение через макросы 
 
### ШАГ 4: Установка макросов 
1. Откройте редактор макросов (Сервис > Макросы) 
2. Создайте модуль "AutoFillModule" 
3. Скопируйте код из файла macros.bas 
 
### ГОТОВО! Система готова к использованию. 
