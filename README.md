# 🔧 АВТОМАТИЧЕСКАЯ НАСТРОЙКА АВТОЗАПОЛНЕНИЯ LIBREOFFICE BASE

## 📋 Электронный журнал выпуска продукции с автозаполнением полей

---

## 🚀 БЫСТРЫЙ СТАРТ (5 МИНУТ)

### **Запустите автоматическую настройку:**
```bash
# Дважды кликните на файл:
ЗАПУСК_АВТОМАТИЧЕСКОЙ_НАСТРОЙКИ.bat
```

### **Выберите "1. БЫСТРАЯ НАСТРОЙКА"** и следуйте инструкциям на экране.

---

## ✨ ЧТО БУДЕТ НАСТРОЕНО АВТОМАТИЧЕСКИ:

✅ **Автозаполнение инвентарного номера** при выборе материала из списка  
✅ **Автозаполнение трудозатрат на единицу** при выборе материала  
✅ **Автоматический расчет общих трудозатрат** при вводе количества  
✅ **Поиск материала по названию** с автозаполнением всех связанных полей  
✅ **Готовые SQL-запросы** для создания отчетов по расходу материалов  
✅ **Макросы LibreOffice Basic** для обработки событий формы  

---

## 📁 СТРУКТУРА ФАЙЛОВ

### **🔧 Основные файлы для настройки:**
- **`ЗАПУСК_АВТОМАТИЧЕСКОЙ_НАСТРОЙКИ.bat`** - главный скрипт автоматической настройки
- **`create_tables.sql`** - SQL-скрипт для создания структуры базы данных
- **`macros.bas`** - макросы LibreOffice Basic для автозаполнения
- **`queries.sql`** - готовые SQL-запросы для отчетов

### **📋 Инструкции и документация:**
- **`ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md`** - подробная пошаговая инструкция
- **`ИНСТРУКЦИЯ_ПО_СОЗДАНИЮ_ФОРМ.md`** - руководство по созданию форм
- **`НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ_И_ОТЧЁТОВ.md`** - настройка отчетов

### **⚙️ Дополнительные файлы:**
- **`автоматическая_настройка_форм.py`** - Python-скрипт для создания XML-шаблонов
- **`форма_журнал_выпуска_шаблон.xml`** - XML-шаблон формы
- **`АВТОМАТИЧЕСКАЯ_НАСТРОЙКА.bat`** - альтернативный скрипт настройки

---

## 🎯 ПРИНЦИП РАБОТЫ АВТОЗАПОЛНЕНИЯ

### **Сценарий 1: Выбор материала из списка**
1. Пользователь выбирает материал из выпадающего списка
2. Макрос `OnMaterialChange` автоматически:
   - Находит материал в таблице МАТЕРИАЛЫ по ID
   - Заполняет поле "Инвентарный номер"
   - Заполняет поле "Трудозатраты на единицу"
   - Пересчитывает общие трудозатраты

### **Сценарий 2: Поиск по названию материала**
1. Пользователь начинает вводить название материала в поле поиска
2. Макрос `OnMaterialNameChange` автоматически:
   - Ищет материал по частичному совпадению названия
   - Заполняет все связанные поля
   - Устанавливает соответствующий материал в выпадающем списке

### **Сценарий 3: Автоматический расчет трудозатрат**
1. Пользователь вводит или изменяет количество
2. Макрос `OnQuantityChange` автоматически:
   - Рассчитывает общие трудозатраты (количество × трудозатраты на единицу)
   - Обновляет поле "Общие трудозатраты"

---

## 🗄️ СТРУКТУРА БАЗЫ ДАННЫХ

### **Таблица МАТЕРИАЛЫ:**
- `ID` - уникальный идентификатор (автоинкремент)
- `НАИМЕНОВАНИЕ` - название материала
- `ИНВЕНТАРНЫЙ_НОМЕР` - уникальный инвентарный номер
- `ЕДИНИЦА_ИЗМЕРЕНИЯ` - единица измерения (шт, м, кг и т.д.)
- `ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ` - трудозатраты на единицу материала
- `ОПИСАНИЕ` - дополнительное описание
- `ДАТА_СОЗДАНИЯ` - дата создания записи

### **Таблица ЖУРНАЛ_ВЫПУСКА:**
- `ID` - уникальный идентификатор записи
- `ДАТА_ВЫПУСКА` - дата выпуска продукции
- `МАТЕРИАЛ_ID` - ссылка на материал (внешний ключ)
- `КОЛИЧЕСТВО` - количество использованного материала
- `ОБЩИЕ_ТРУДОЗАТРАТЫ` - рассчитанные общие трудозатраты
- `ПРИМЕЧАНИЯ` - дополнительные примечания
- `ДАТА_СОЗДАНИЯ` - дата создания записи

---

## 📊 ГОТОВЫЕ ОТЧЕТЫ

### **1. Отчет по расходу материалов за месяц**
```sql
SELECT 
    m.ИНВЕНТАРНЫЙ_НОМЕР as "Инвентарный номер",
    m.НАИМЕНОВАНИЕ as "Наименование материала", 
    m.ЕДИНИЦА_ИЗМЕРЕНИЯ as "Ед. изм.",
    SUM(j.КОЛИЧЕСТВО) as "Количество за месяц",
    SUM(j.ОБЩИЕ_ТРУДОЗАТРАТЫ) as "Общие трудозатраты"
FROM ЖУРНАЛ_ВЫПУСКА j 
JOIN МАТЕРИАЛЫ m ON j.МАТЕРИАЛ_ID = m.ID 
WHERE YEAR(j.ДАТА_ВЫПУСКА) = :ГОД AND MONTH(j.ДАТА_ВЫПУСКА) = :МЕСЯЦ 
GROUP BY m.ID, m.ИНВЕНТАРНЫЙ_НОМЕР, m.НАИМЕНОВАНИЕ, m.ЕДИНИЦА_ИЗМЕРЕНИЯ
ORDER BY SUM(j.КОЛИЧЕСТВО) DESC
```

### **2. Отчет за произвольный период**
```sql
SELECT 
    m.ИНВЕНТАРНЫЙ_НОМЕР as "Инвентарный номер",
    m.НАИМЕНОВАНИЕ as "Наименование материала", 
    SUM(j.КОЛИЧЕСТВО) as "Количество",
    SUM(j.ОБЩИЕ_ТРУДОЗАТРАТЫ) as "Трудозатраты",
    j.ДАТА_ВЫПУСКА as "Дата"
FROM ЖУРНАЛ_ВЫПУСКА j 
JOIN МАТЕРИАЛЫ m ON j.МАТЕРИАЛ_ID = m.ID 
WHERE j.ДАТА_ВЫПУСКА BETWEEN :ДАТА_НАЧАЛА AND :ДАТА_ОКОНЧАНИЯ
GROUP BY m.ID, m.ИНВЕНТАРНЫЙ_НОМЕР, m.НАИМЕНОВАНИЕ, j.ДАТА_ВЫПУСКА
ORDER BY j.ДАТА_ВЫПУСКА DESC
```

---

## ⚙️ ТЕХНИЧЕСКИЕ ТРЕБОВАНИЯ

### **Программное обеспечение:**
- **LibreOffice Base** (версия 6.0 или выше)
- **Windows** (7/8/10/11)
- **Python 3.x** (опционально, для дополнительных скриптов)

### **Настройки безопасности:**
- Уровень безопасности макросов: **Средний**
- Разрешение выполнения макросов для документа

---

## 🔧 УСТРАНЕНИЕ НЕПОЛАДОК

### **❓ Макросы не работают:**
- Проверьте настройки безопасности макросов (Сервис → Параметры → Безопасность)
- Убедитесь, что при открытии БД нажали "Разрешить макросы"
- Проверьте, что макросы загружены в правильный модуль

### **❓ Автозаполнение не работает:**
- Проверьте точность имен полей в форме
- Убедитесь, что события правильно привязаны к макросам
- Проверьте, что таблицы созданы и содержат данные

### **❓ Ошибки при импорте SQL:**
- Выполняйте SQL-команды по одной
- Проверьте кодировку файла (должна быть UTF-8)
- Убедитесь, что используется встроенная база данных HSQLDB

---

## 📞 ПОДДЕРЖКА

При возникновении проблем:

1. **Запустите диагностику:** `ЗАПУСК_АВТОМАТИЧЕСКОЙ_НАСТРОЙКИ.bat` → "5. Помощь и диагностика"
2. **Изучите подробные инструкции:** `ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md`
3. **Проверьте все файлы:** убедитесь, что все необходимые файлы присутствуют

---

## 🎉 РЕЗУЛЬТАТ

После успешной настройки вы получите:

✅ **Полностью автоматизированную форму ввода данных**  
✅ **Экономию времени до 80%** при заполнении журнала  
✅ **Исключение ошибок** при вводе инвентарных номеров  
✅ **Автоматический расчет** трудозатрат  
✅ **Готовые отчеты** по расходу материалов  
✅ **Удобный поиск** материалов по названию  

**💡 Время настройки: 5-10 минут**  
**🚀 Готовность к работе: сразу после настройки**
