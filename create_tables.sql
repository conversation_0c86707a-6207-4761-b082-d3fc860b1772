-- Создание структуры БД для LibreOffice Base 
-- Электронный журнал выпуска продукции 
-- Автоматически сгенерировано 03.08.2025 19:21:38,95 
 
-- Таблица материалов 
CREATE TABLE МАТЕРИАЛЫ ( 
    ID INTEGER IDENTITY PRIMARY KEY, 
    НАИМЕНОВАНИЕ VARCHAR(200) NOT NULL, 
    ИНВЕНТАРНЫЙ_НОМЕР VARCHAR(50) UNIQUE NOT NULL, 
    ЕДИНИЦА_ИЗМЕРЕНИЯ VARCHAR(20) NOT NULL, 
    ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ DECIMAL(10,2) DEFAULT 0, 
    ОПИСАНИЕ VARCHAR(500), 
    ДАТА_СОЗДАНИЯ TIMESTAMP DEFAULT CURRENT_TIMESTAMP 
); 
 
-- Таблица журнала выпуска 
CREATE TABLE ЖУРНАЛ_ВЫПУСКА ( 
    ID INTEGER IDENTITY PRIMARY KEY, 
    ДАТА_ВЫПУСКА DATE NOT NULL, 
    МАТЕРИАЛ_ID INTEGER NOT NULL, 
    КОЛИЧЕСТВО DECIMAL(10,2) NOT NULL, 
    ОБЩИЕ_ТРУДОЗАТРАТЫ DECIMAL(10,2), 
    ПРИМЕЧАНИЯ VARCHAR(500), 
    ДАТА_СОЗДАНИЯ TIMESTAMP DEFAULT CURRENT_TIMESTAMP, 
    FOREIGN KEY (МАТЕРИАЛ_ID) REFERENCES МАТЕРИАЛЫ(ID) 
); 
 
-- Создание индексов 
CREATE INDEX IDX_ЖУРНАЛ_ДАТА ON ЖУРНАЛ_ВЫПУСКА(ДАТА_ВЫПУСКА); 
CREATE INDEX IDX_ЖУРНАЛ_МАТЕРИАЛ ON ЖУРНАЛ_ВЫПУСКА(МАТЕРИАЛ_ID); 
CREATE INDEX IDX_МАТЕРИАЛЫ_НОМЕР ON МАТЕРИАЛЫ(ИНВЕНТАРНЫЙ_НОМЕР); 
 
-- Тестовые данные 
INSERT INTO МАТЕРИАЛЫ (НАИМЕНОВАНИЕ, ИНВЕНТАРНЫЙ_НОМЕР, ЕДИНИЦА_ИЗМЕРЕНИЯ, ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ, ОПИСАНИЕ) VALUES 
('Болт М8x20', 'БЛТ-001', 'шт', 0.5, 'Болт с метрической резьбой'), 
('Гайка М8', 'ГК-001', 'шт', 0.3, 'Гайка метрическая'), 
('Шайба 8мм', 'ШБ-001', 'шт', 0.1, 'Шайба плоская'), 
('Лист стальной 2мм', 'ЛС-002', 'м²', 2.5, 'Лист стальной холоднокатаный'), 
('Профиль 20x20', 'ПР-020', 'м', 1.8, 'Профиль квадратный'); 
