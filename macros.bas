REM Макросы для автозаполнения LibreOffice Base 
REM Электронный журнал выпуска продукции 
 
REM Макрос автозаполнения при выборе материала по ID
Sub OnMaterialChange(oEvent) 
    Dim oForm As Object 
    Dim oMaterialCombo As Object 
    Dim oConnection As Object 
    Dim oStatement As Object 
    Dim oResultSet As Object 
    Dim sSql As String 
 
    oForm = oEvent.Source.getModel().getParent() 
    oMaterialCombo = oForm.getByName("МАТЕРИАЛ_ID") 
 
    If oMaterialCombo.getCurrentValue() <> "" Then 
        oConnection = oForm.getParent().getConnection() 
        sSql = "SELECT ИНВЕНТАРНЫЙ_НОМЕР, ЕДИНИЦА_ИЗМЕРЕНИЯ, ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ FROM МАТЕРИАЛЫ WHERE ID = " & oMaterialCombo.getCurrentValue() 
        oStatement = oConnection.createStatement() 
        oResultSet = oStatement.executeQuery(sSql) 
 
        If oResultSet.next() Then 
            SetFieldValue(oForm, "ИНВЕНТАРНЫЙ_НОМЕР", oResultSet.getString(1))
            SetFieldValue(oForm, "ЕДИНИЦА_ИЗМЕРЕНИЯ", oResultSet.getString(2))
            SetFieldValue(oForm, "ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ", oResultSet.getDouble(3))
            CalculateTotalLabor(oForm) 
        End If 
        oResultSet.close() 
        oStatement.close() 
    End If 
End Sub 

REM Макрос автозаполнения при вводе наименования материала
Sub OnMaterialNameChange(oEvent)
    Dim oForm As Object
    Dim oNameField As Object
    Dim oConnection As Object
    Dim oStatement As Object
    Dim oResultSet As Object
    Dim sSql As String
    Dim sName As String
    
    oForm = oEvent.Source.getModel().getParent()
    oNameField = oEvent.Source
    sName = Trim(oNameField.getText())
    
    If Len(sName) > 2 Then ' Начинаем поиск после ввода 3 символов
        oConnection = oForm.getParent().getConnection()
        sSql = "SELECT ID, ИНВЕНТАРНЫЙ_НОМЕР, ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ FROM МАТЕРИАЛЫ WHERE UPPER(НАИМЕНОВАНИЕ) LIKE UPPER('%" & sName & "%') ORDER BY НАИМЕНОВАНИЕ LIMIT 1"
        oStatement = oConnection.createStatement()
        oResultSet = oStatement.executeQuery(sSql)
        
        If oResultSet.next() Then
            SetFieldValue(oForm, "МАТЕРИАЛ_ID", oResultSet.getLong(1))
            SetFieldValue(oForm, "ИНВЕНТАРНЫЙ_НОМЕР", oResultSet.getString(2))
            SetFieldValue(oForm, "ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ", oResultSet.getDouble(3))
            CalculateTotalLabor(oForm)
        End If
        oResultSet.close()
        oStatement.close()
    End If
End Sub

REM Пересчет трудозатрат при изменении количества
Sub OnQuantityChange(oEvent)
    Dim oForm As Object
    oForm = oEvent.Source.getModel().getParent()
    CalculateTotalLabor(oForm)
End Sub
 
REM Пересчет трудозатрат 
Sub CalculateTotalLabor(oForm As Object) 
    Dim dQuantity As Double 
    Dim dLaborPerUnit As Double 
    
    On Error Resume Next
    dQuantity = GetFieldValue(oForm, "КОЛИЧЕСТВО")
    dLaborPerUnit = GetFieldValue(oForm, "ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ")
    
    If dQuantity > 0 And dLaborPerUnit > 0 Then
        SetFieldValue(oForm, "ОБЩИЕ_ТРУДОЗАТРАТЫ", dQuantity * dLaborPerUnit)
    End If
End Sub

REM Вспомогательная функция для безопасного получения значения поля
Function GetFieldValue(oForm As Object, sFieldName As String) As Variant
    Dim oField As Object
    On Error Resume Next
    oField = oForm.getByName(sFieldName)
    If Not IsNull(oField) Then
        If oField.supportsService("com.sun.star.form.component.NumericField") Then
            GetFieldValue = oField.getValue()
        Else
            GetFieldValue = oField.getText()
        End If
    Else
        GetFieldValue = 0
    End If
End Function

REM Вспомогательная функция для безопасной установки значения поля
Sub SetFieldValue(oForm As Object, sFieldName As String, vValue As Variant)
    Dim oField As Object
    On Error Resume Next
    oField = oForm.getByName(sFieldName)
    If Not IsNull(oField) Then
        If oField.supportsService("com.sun.star.form.component.NumericField") Then
            oField.setValue(vValue)
        Else
            oField.setText(CStr(vValue))
        End If
    End If
End Sub

REM Макрос для создания отчёта по материалам за месяц
Sub CreateMonthlyReport()
    Dim oDoc As Object
    Dim oConnection As Object
    Dim oStatement As Object
    Dim oResultSet As Object
    Dim sSql As String
    Dim iYear As Integer
    Dim iMonth As Integer
    
    ' Получаем текущий год и месяц
    iYear = Year(Now())
    iMonth = Month(Now())
    
    ' Запрашиваем у пользователя год и месяц
    iYear = InputBox("Введите год для отчёта:", "Год", iYear)
    iMonth = InputBox("Введите месяц для отчёта (1-12):", "Месяц", iMonth)
    
    If iYear > 0 And iMonth >= 1 And iMonth <= 12 Then
        oDoc = ThisComponent
        oConnection = oDoc.getDataSource().getConnection("", "")
        
        sSql = "SELECT m.ИНВЕНТАРНЫЙ_НОМЕР, m.НАИМЕНОВАНИЕ, m.ЕДИНИЦА_ИЗМЕРЕНИЯ, " & _
               "SUM(j.КОЛИЧЕСТВО) as КОЛИЧЕСТВО_ЗА_МЕСЯЦ, SUM(j.ОБЩИЕ_ТРУДОЗАТРАТЫ) as ТРУДОЗАТРАТЫ " & _
               "FROM ЖУРНАЛ_ВЫПУСКА j JOIN МАТЕРИАЛЫ m ON j.МАТЕРИАЛ_ID = m.ID " & _
               "WHERE YEAR(j.ДАТА_ВЫПУСКА) = " & iYear & " AND MONTH(j.ДАТА_ВЫПУСКА) = " & iMonth & " " & _
               "GROUP BY m.ID, m.ИНВЕНТАРНЫЙ_НОМЕР, m.НАИМЕНОВАНИЕ, m.ЕДИНИЦА_ИЗМЕРЕНИЯ " & _
               "ORDER BY SUM(j.КОЛИЧЕСТВО) DESC"
        
        oStatement = oConnection.createStatement()
        oResultSet = oStatement.executeQuery(sSql)
        
        ' Здесь можно добавить код для вывода результатов в таблицу или отчёт
        MsgBox("Отчёт за " & iMonth & "/" & iYear & " готов к просмотру")
        
        oResultSet.close()
        oStatement.close()
        oConnection.close()
    End If
End Sub 
