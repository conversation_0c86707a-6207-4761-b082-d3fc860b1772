REM Макросы для автозаполнения LibreOffice Base 
REM Электронный журнал выпуска продукции 
 
REM Макрос автозаполнения при выборе материала 
Sub OnMaterialChange(oEvent) 
    Dim oForm As Object 
    Dim oMaterialCombo As Object 
    Dim oConnection As Object 
    Dim oStatement As Object 
    Dim oResultSet As Object 
    Dim sSql As String 
 
    oForm = oEvent.Source.getModel().getParent() 
    oMaterialCombo = oForm.getByName("МАТЕРИАЛ_ID") 
 
    If oMaterialCombo.getCurrentValue() <> "" Then 
        oConnection = oForm.getParent().getConnection() 
        sSql = "SELECT ИНВЕНТАРНЫЙ_НОМЕР, ЕДИНИЦА_ИЗМЕРЕНИЯ, ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ FROM МАТЕРИАЛЫ WHERE ID = " & oMaterialCombo.getCurrentValue() 
        oStatement = oConnection.createStatement() 
        oResultSet = oStatement.executeQuery(sSql) 
 
        If oResultSet.next() Then 
            oForm.getByName("ИНВЕНТАРНЫЙ_НОМЕР").setString(oResultSet.getString(1)) 
            oForm.getByName("ЕДИНИЦА_ИЗМЕРЕНИЯ").setString(oResultSet.getString(2)) 
            oForm.getByName("ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ").setDouble(oResultSet.getDouble(3)) 
            CalculateTotalLabor(oForm) 
        End If 
        oResultSet.close() 
        oStatement.close() 
    End If 
End Sub 
 
REM Пересчет трудозатрат 
Sub CalculateTotalLabor(oForm As Object) 
    Dim dQuantity As Double 
    Dim dLaborPerUnit As Double 
    dQuantity = oForm.getByName("КОЛИЧЕСТВО").getDouble() 
    dLaborPerUnit = oForm.getByName("ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ").getDouble() 
    oForm.getByName("ОБЩИЕ_ТРУДОЗАТРАТЫ").setDouble(dQuantity * dLaborPerUnit) 
End Sub 
