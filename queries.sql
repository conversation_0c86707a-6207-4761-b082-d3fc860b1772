-- Готовые запросы для LibreOffice Base 
-- Электронный журнал выпуска продукции 
 
-- 1. Информация о материале для автозаполнения
SELECT ID, НАИМЕНОВАНИЕ, ИНВЕНТАРНЫЙ_НОМЕР, ЕДИНИЦА_ИЗМЕРЕНИЯ, ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ, ОПИСАНИЕ 
FROM МАТЕРИАЛЫ WHERE ID = :МАТЕРИАЛ_ID; 

-- 2. Поиск материала по наименованию для автозаполнения
SELECT ID, ИНВЕНТАРНЫЙ_НОМЕР, ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ 
FROM МАТЕРИАЛЫ 
WHERE UPPER(НАИМЕНОВАНИЕ) LIKE UPPER(:НАИМЕНОВАНИЕ_ПОИСК);

-- 3. <PERSON><PERSON><PERSON><PERSON><PERSON> с автозаполнением 
SELECT j.ID, j.ДАТА_ВЫПУСКА, j.МАТЕРИАЛ_ID, m.НАИМЕНОВАНИЕ as НАЗВАНИЕ_МАТЕРИАЛА, 
       m.ИНВЕНТАРНЫЙ_НОМЕР, m.ЕДИНИЦА_ИЗМЕРЕНИЯ, j.КОЛИЧЕСТВО, 
       m.ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ, j.ОБЩИЕ_ТРУДОЗАТРАТЫ, j.ПРИМЕЧАНИЯ 
FROM ЖУРНАЛ_ВЫПУСКА j LEFT JOIN МАТЕРИАЛЫ m ON j.МАТЕРИАЛ_ID = m.ID 
ORDER BY j.ДАТА_ВЫПУСКА DESC, j.ID DESC; 

-- 4. ОТЧЁТ ПО РАСХОДУ МАТЕРИАЛОВ ЗА МЕСЯЦ
SELECT 
    m.ИНВЕНТАРНЫЙ_НОМЕР as "Инвентарный номер",
    m.НАИМЕНОВАНИЕ as "Наименование материала", 
    m.ЕДИНИЦА_ИЗМЕРЕНИЯ as "Ед. изм.",
    SUM(j.КОЛИЧЕСТВО) as "Количество за месяц",
    SUM(j.ОБЩИЕ_ТРУДОЗАТРАТЫ) as "Общие трудозатраты",
    COUNT(j.ID) as "Количество операций",
    MIN(j.ДАТА_ВЫПУСКА) as "Первая операция",
    MAX(j.ДАТА_ВЫПУСКА) as "Последняя операция"
FROM ЖУРНАЛ_ВЫПУСКА j 
JOIN МАТЕРИАЛЫ m ON j.МАТЕРИАЛ_ID = m.ID 
WHERE YEAR(j.ДАТА_ВЫПУСКА) = :ГОД AND MONTH(j.ДАТА_ВЫПУСКА) = :МЕСЯЦ 
GROUP BY m.ID, m.ИНВЕНТАРНЫЙ_НОМЕР, m.НАИМЕНОВАНИЕ, m.ЕДИНИЦА_ИЗМЕРЕНИЯ
ORDER BY SUM(j.КОЛИЧЕСТВО) DESC;

-- 5. ОТЧЁТ С ФИЛЬТРОМ ПО ДАТЕ (для удобного выбора периода)
SELECT 
    m.ИНВЕНТАРНЫЙ_НОМЕР as "Инвентарный номер",
    m.НАИМЕНОВАНИЕ as "Наименование материала", 
    m.ЕДИНИЦА_ИЗМЕРЕНИЯ as "Ед. изм.",
    SUM(j.КОЛИЧЕСТВО) as "Количество",
    SUM(j.ОБЩИЕ_ТРУДОЗАТРАТЫ) as "Трудозатраты",
    j.ДАТА_ВЫПУСКА as "Дата"
FROM ЖУРНАЛ_ВЫПУСКА j 
JOIN МАТЕРИАЛЫ m ON j.МАТЕРИАЛ_ID = m.ID 
WHERE j.ДАТА_ВЫПУСКА BETWEEN :ДАТА_НАЧАЛА AND :ДАТА_ОКОНЧАНИЯ
GROUP BY m.ID, m.ИНВЕНТАРНЫЙ_НОМЕР, m.НАИМЕНОВАНИЕ, m.ЕДИНИЦА_ИЗМЕРЕНИЯ, j.ДАТА_ВЫПУСКА
ORDER BY j.ДАТА_ВЫПУСКА DESC, SUM(j.КОЛИЧЕСТВО) DESC;

-- 6. Список материалов для выпадающего списка
SELECT ID, НАИМЕНОВАНИЕ, ИНВЕНТАРНЫЙ_НОМЕР 
FROM МАТЕРИАЛЫ 
ORDER BY НАИМЕНОВАНИЕ;

-- 7. Проверка существования материала по наименованию
SELECT COUNT(*) as НАЙДЕНО, ID, ИНВЕНТАРНЫЙ_НОМЕР, ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ
FROM МАТЕРИАЛЫ 
WHERE UPPER(TRIM(НАИМЕНОВАНИЕ)) = UPPER(TRIM(:НАИМЕНОВАНИЕ))
GROUP BY ID, ИНВЕНТАРНЫЙ_НОМЕР, ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ; 
