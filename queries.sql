-- Готовые запросы для LibreOffice Base 
-- Электронный журнал выпуска продукции 
 
-- 1. Информация о материале 
SELECT ID, НАИМЕНОВАНИЕ, ИНВЕНТАРНЫЙ_НОМЕР, ЕДИНИЦА_ИЗМЕРЕНИЯ, ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ, ОПИСАНИЕ 
FROM МАТЕРИАЛЫ WHERE ID = :МАТЕРИАЛ_ID; 
 
-- 2. Журнал с автозаполнением 
SELECT j.ID, j.ДАТА_ВЫПУСКА, j.МАТЕРИАЛ_ID, m.НАИМЕНОВАНИЕ as НАЗВАНИЕ_МАТЕРИАЛА, 
       m.ИНВЕНТАРНЫЙ_НОМЕР, m.ЕДИНИЦА_ИЗМЕРЕНИЯ, j.КОЛИЧЕСТВО, 
       m.ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ, j.ОБЩИЕ_ТРУДОЗАТРАТЫ, j.ПРИМЕЧАНИЯ 
FROM ЖУРНАЛ_ВЫПУСКА j LEFT JOIN МАТЕРИАЛЫ m ON j.МАТЕРИАЛ_ID = m.ID 
ORDER BY j.ДАТА_ВЫПУСКА DESC, j.ID DESC; 
 
-- 3. Ежемесячный отчет 
SELECT m.НАИМЕНОВАНИЕ, m.ИНВЕНТАРНЫЙ_НОМЕР, m.ЕДИНИЦА_ИЗМЕРЕНИЯ, 
       COUNT(j.ID) as КОЛИЧЕСТВО_ОПЕРАЦИЙ 
FROM ЖУРНАЛ_ВЫПУСКА j JOIN МАТЕРИАЛЫ m ON j.МАТЕРИАЛ_ID = m.ID 
WHERE YEAR(j.ДАТА_ВЫПУСКА) = :ГОД AND MONTH(j.ДАТА_ВЫПУСКА) = :МЕСЯЦ 
ORDER BY ОБЩИЕ_ТРУДОЗАТРАТЫ DESC; 
