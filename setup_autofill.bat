@echo off
chcp 65001 >nul
title Setup AutoFill for LibreOffice Base

echo.
echo ==========================================
echo    SETUP AUTOFILL FOR LIBREOFFICE BASE
echo    Electronic Production Journal
echo ==========================================
echo.

:: Check if database exists
set "DB_FILE=1.4 Elektronnyy zhurnal vypuska produktsii.odb"
if not exist "%DB_FILE%" (
    echo Database file not found: %DB_FILE%
    echo Please make sure the database file is in current folder
    pause
    exit /b 1
)

echo Database found: %DB_FILE%
echo.

:: Find LibreOffice
echo Searching for LibreOffice...
set "LIBREOFFICE_PATH="
for %%d in (C D E F G H) do (
    if exist "%%d:\Program Files\LibreOffice\program\soffice.exe" (
        set "LIBREOFFICE_PATH=%%d:\Program Files\LibreOffice\program\"
        goto :found_libreoffice
    )
    if exist "%%d:\Program Files (x86)\LibreOffice\program\soffice.exe" (
        set "LIBREOFFICE_PATH=%%d:\Program Files (x86)\LibreOffice\program\"
        goto :found_libreoffice
    )
)

echo LibreOffice not found!
echo Please install LibreOffice from official website
pause
exit /b 1

:found_libreoffice
echo LibreOffice found: %LIBREOFFICE_PATH%
echo.

:: Main menu
:main_menu
echo ==========================================
echo              MAIN MENU
echo ==========================================
echo.
echo 1. OPEN DATABASE AND SETUP AUTOFILL
echo 2. CREATE MACROS ONLY
echo 3. SHOW INSTRUCTIONS
echo 0. EXIT
echo.
set /p choice="Choose action (0-3): "

if "%choice%"=="1" goto :setup_autofill
if "%choice%"=="2" goto :create_macros_only
if "%choice%"=="3" goto :show_instructions
if "%choice%"=="0" goto :exit

echo Invalid choice! Try again.
goto :main_menu

:: Setup autofill
:setup_autofill
echo.
echo ==========================================
echo         SETUP AUTOFILL
echo ==========================================
echo.

echo Opening your existing database...
start "" "%LIBREOFFICE_PATH%soffice.exe" --base "%CD%\%DB_FILE%"

echo.
echo Waiting for LibreOffice Base to load...
timeout /t 5 /nobreak >nul

echo.
echo ==========================================
echo       STEP-BY-STEP INSTRUCTIONS
echo ==========================================
echo.

echo STEP 1: ANALYZE YOUR DATABASE STRUCTURE
echo ----------------------------------------
echo • In LibreOffice Base go to "Tables" section
echo • Find your materials table and production journal table
echo • Write down exact table names and field names
echo.
pause

echo STEP 2: CREATE AND ADAPT MACROS
echo --------------------------------
echo • Creating macros file...
call :create_macros
start notepad "macros.bas"
echo • Open macros.bas file
echo • Replace "YOUR_MATERIALS_TABLE" with your real table name
echo • Replace "YOUR_JOURNAL_TABLE" with your real table name
echo • Check field names (ID, NAME, INVENTORY_NUMBER, etc.)
echo.
pause

echo STEP 3: INSTALL MACROS
echo -----------------------
echo • In LibreOffice Base press Alt+F11
echo • Create module "AutoFillModule"
echo • Copy adapted code from macros.bas
echo.
pause

echo STEP 4: SETUP FORM
echo -------------------
echo • Create or edit form based on your journal table
echo • Add fields with these names:
echo   - MATERIAL_ID (dropdown list)
echo   - MATERIAL_SEARCH (search field)
echo   - INVENTORY_NUMBER (autofill, read-only)
echo   - QUANTITY (input field)
echo   - LABOR_COST_PER_UNIT (autofill, read-only)
echo   - TOTAL_LABOR_COST (autofill, read-only)
echo.
echo • Setup events:
echo   - MATERIAL_ID → OnMaterialChange
echo   - MATERIAL_SEARCH → OnMaterialNameChange
echo   - QUANTITY → OnQuantityChange
echo.
pause

echo STEP 5: TEST FUNCTIONALITY
echo ---------------------------
echo • Open created form
echo • Test material selection from dropdown
echo • Test material search by name
echo • Test quantity input and labor cost calculation
echo.

goto :success

:: Create macros only
:create_macros_only
echo.
echo Creating macros for autofill...
call :create_macros
echo Macros file created: macros.bas
start notepad "macros.bas"
echo.
echo IMPORTANT: Adapt table names in macros.bas file!
echo Replace YOUR_MATERIALS_TABLE and YOUR_JOURNAL_TABLE
echo with your real table names.
echo.
pause
goto :main_menu

:create_macros
if exist "macros.bas" (
    echo Macros file already exists
    goto :eof
)

echo Creating macros.bas file...

echo REM Macros for LibreOffice Base autofill > "macros.bas"
echo REM Electronic Production Journal >> "macros.bas"
echo REM Adapted for existing database >> "macros.bas"
echo. >> "macros.bas"
echo REM IMPORTANT: Change table names according to your database! >> "macros.bas"
echo REM Replace YOUR_MATERIALS_TABLE with your real materials table name >> "macros.bas"
echo REM Replace YOUR_JOURNAL_TABLE with your real journal table name >> "macros.bas"
echo. >> "macros.bas"
echo REM Macro for autofill when material is selected by ID >> "macros.bas"
echo Sub OnMaterialChange(oEvent) >> "macros.bas"
echo     Dim oForm As Object >> "macros.bas"
echo     Dim oMaterialCombo As Object >> "macros.bas"
echo     Dim oConnection As Object >> "macros.bas"
echo     Dim oStatement As Object >> "macros.bas"
echo     Dim oResultSet As Object >> "macros.bas"
echo     Dim sSql As String >> "macros.bas"
echo. >> "macros.bas"
echo     oForm = oEvent.Source.getModel().getParent() >> "macros.bas"
echo     oMaterialCombo = oForm.getByName("MATERIAL_ID") >> "macros.bas"
echo. >> "macros.bas"
echo     If oMaterialCombo.getCurrentValue() ^<^> "" Then >> "macros.bas"
echo         oConnection = oForm.getParent().getConnection() >> "macros.bas"
echo         REM CHANGE table name to your materials table >> "macros.bas"
echo         sSql = "SELECT INVENTORY_NUMBER, UNIT, LABOR_COST_PER_UNIT FROM YOUR_MATERIALS_TABLE WHERE ID = " ^& oMaterialCombo.getCurrentValue() >> "macros.bas"
echo         oStatement = oConnection.createStatement() >> "macros.bas"
echo         oResultSet = oStatement.executeQuery(sSql) >> "macros.bas"
echo. >> "macros.bas"
echo         If oResultSet.next() Then >> "macros.bas"
echo             SetFieldValue(oForm, "INVENTORY_NUMBER", oResultSet.getString(1)) >> "macros.bas"
echo             SetFieldValue(oForm, "UNIT", oResultSet.getString(2)) >> "macros.bas"
echo             SetFieldValue(oForm, "LABOR_COST_PER_UNIT", oResultSet.getDouble(3)) >> "macros.bas"
echo             CalculateTotalLabor(oForm) >> "macros.bas"
echo         End If >> "macros.bas"
echo         oResultSet.close() >> "macros.bas"
echo         oStatement.close() >> "macros.bas"
echo     End If >> "macros.bas"
echo End Sub >> "macros.bas"
echo. >> "macros.bas"
echo REM Macro for autofill when material name is entered >> "macros.bas"
echo Sub OnMaterialNameChange(oEvent) >> "macros.bas"
echo     Dim oForm As Object >> "macros.bas"
echo     Dim oNameField As Object >> "macros.bas"
echo     Dim oConnection As Object >> "macros.bas"
echo     Dim oStatement As Object >> "macros.bas"
echo     Dim oResultSet As Object >> "macros.bas"
echo     Dim sSql As String >> "macros.bas"
echo     Dim sName As String >> "macros.bas"
echo. >> "macros.bas"
echo     oForm = oEvent.Source.getModel().getParent() >> "macros.bas"
echo     oNameField = oEvent.Source >> "macros.bas"
echo     sName = Trim(oNameField.getText()) >> "macros.bas"
echo. >> "macros.bas"
echo     If Len(sName) ^> 2 Then >> "macros.bas"
echo         oConnection = oForm.getParent().getConnection() >> "macros.bas"
echo         REM CHANGE table name to your materials table >> "macros.bas"
echo         sSql = "SELECT ID, INVENTORY_NUMBER, LABOR_COST_PER_UNIT FROM YOUR_MATERIALS_TABLE WHERE UPPER(NAME) LIKE UPPER('%" ^& sName ^& "%') ORDER BY NAME LIMIT 1" >> "macros.bas"
echo         oStatement = oConnection.createStatement() >> "macros.bas"
echo         oResultSet = oStatement.executeQuery(sSql) >> "macros.bas"
echo. >> "macros.bas"
echo         If oResultSet.next() Then >> "macros.bas"
echo             SetFieldValue(oForm, "MATERIAL_ID", oResultSet.getLong(1)) >> "macros.bas"
echo             SetFieldValue(oForm, "INVENTORY_NUMBER", oResultSet.getString(2)) >> "macros.bas"
echo             SetFieldValue(oForm, "LABOR_COST_PER_UNIT", oResultSet.getDouble(3)) >> "macros.bas"
echo             CalculateTotalLabor(oForm) >> "macros.bas"
echo         End If >> "macros.bas"
echo         oResultSet.close() >> "macros.bas"
echo         oStatement.close() >> "macros.bas"
echo     End If >> "macros.bas"
echo End Sub >> "macros.bas"
echo. >> "macros.bas"
echo REM Recalculate labor costs when quantity changes >> "macros.bas"
echo Sub OnQuantityChange(oEvent) >> "macros.bas"
echo     Dim oForm As Object >> "macros.bas"
echo     oForm = oEvent.Source.getModel().getParent() >> "macros.bas"
echo     CalculateTotalLabor(oForm) >> "macros.bas"
echo End Sub >> "macros.bas"
echo. >> "macros.bas"
echo REM Calculate total labor costs >> "macros.bas"
echo Sub CalculateTotalLabor(oForm As Object) >> "macros.bas"
echo     Dim dQuantity As Double >> "macros.bas"
echo     Dim dLaborPerUnit As Double >> "macros.bas"
echo. >> "macros.bas"
echo     On Error Resume Next >> "macros.bas"
echo     dQuantity = GetFieldValue(oForm, "QUANTITY") >> "macros.bas"
echo     dLaborPerUnit = GetFieldValue(oForm, "LABOR_COST_PER_UNIT") >> "macros.bas"
echo. >> "macros.bas"
echo     If dQuantity ^> 0 And dLaborPerUnit ^> 0 Then >> "macros.bas"
echo         SetFieldValue(oForm, "TOTAL_LABOR_COST", dQuantity * dLaborPerUnit) >> "macros.bas"
echo     End If >> "macros.bas"
echo End Sub >> "macros.bas"
echo. >> "macros.bas"
echo REM Helper functions >> "macros.bas"
echo Function GetFieldValue(oForm As Object, sFieldName As String) As Variant >> "macros.bas"
echo     Dim oField As Object >> "macros.bas"
echo     On Error Resume Next >> "macros.bas"
echo     oField = oForm.getByName(sFieldName) >> "macros.bas"
echo     If Not IsNull(oField) Then >> "macros.bas"
echo         If oField.supportsService("com.sun.star.form.component.NumericField") Then >> "macros.bas"
echo             GetFieldValue = oField.getValue() >> "macros.bas"
echo         Else >> "macros.bas"
echo             GetFieldValue = oField.getText() >> "macros.bas"
echo         End If >> "macros.bas"
echo     Else >> "macros.bas"
echo         GetFieldValue = 0 >> "macros.bas"
echo     End If >> "macros.bas"
echo End Function >> "macros.bas"
echo. >> "macros.bas"
echo Sub SetFieldValue(oForm As Object, sFieldName As String, vValue As Variant) >> "macros.bas"
echo     Dim oField As Object >> "macros.bas"
echo     On Error Resume Next >> "macros.bas"
echo     oField = oForm.getByName(sFieldName) >> "macros.bas"
echo     If Not IsNull(oField) Then >> "macros.bas"
echo         If oField.supportsService("com.sun.star.form.component.NumericField") Then >> "macros.bas"
echo             oField.setValue(vValue) >> "macros.bas"
echo         Else >> "macros.bas"
echo             oField.setText(CStr(vValue)) >> "macros.bas"
echo         End If >> "macros.bas"
echo     End If >> "macros.bas"
echo End Sub >> "macros.bas"

echo Macros file created successfully!
goto :eof

:: Show instructions
:show_instructions
echo.
echo Opening instructions...
start notepad "ИНСТРУКЦИЯ_ДЛЯ_СУЩЕСТВУЮЩЕЙ_БД.txt"
goto :main_menu

:success
echo.
echo ==========================================
echo         SETUP COMPLETED!
echo ==========================================
echo.
echo Autofill setup for your existing database completed!
echo.
echo IMPORTANT REMINDERS:
echo • Adapt table names in macros.bas file
echo • Check field names match your database
echo • Test autofill functionality
echo.
echo RESULT:
echo • Autofill inventory number when selecting material
echo • Autofill labor costs when selecting material
echo • Auto-calculate total labor costs when entering quantity
echo • Search materials by name with autofill
echo.
echo Time saved: up to 80 percent when entering data!
echo.
pause
goto :main_menu

:exit
echo.
echo Thank you for using autofill setup!
echo.
pause
exit /b 0
