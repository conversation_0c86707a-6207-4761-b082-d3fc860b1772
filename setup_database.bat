@echo off
chcp 65001 >nul
echo ========================================
echo   АВТОМАТИЧЕСКОЕ СОЗДАНИЕ БД LIBREOFFICE
echo   Электронный журнал выпуска продукции
echo ========================================
echo.

:: Расширенная проверка наличия LibreOffice на всех дисках
set "LIBREOFFICE_PATH="
echo "Поиск LibreOffice..."

:: Поиск в переменных окружения (сначала проверяем PATH)
for %%i in (soffice.exe) do (
    if not "%%~$PATH:i"=="" (
        set "LIBREOFFICE_PATH=%%~dpi"
        echo "Найден в PATH: %%~dpi"
        goto :found_libreoffice
    )
)

:: Проверка стандартных путей на всех дисках
for %%d in (C D E F G H) do (
    if exist "%%d:\Program Files\LibreOffice\program\soffice.exe" (
        set "LIBREOFFICE_PATH=%%d:\Program Files\LibreOffice\program\"
        echo Найден на %%d:\Program Files\LibreOffice\"
        goto :found_libreoffice
    )
    
    if exist "%%d:\Program Files ^(x86^)\LibreOffice\program\soffice.exe" (
        set "LIBREOFFICE_PATH=%%d:\Program Files ^(x86^)\LibreOffice\program\"
        echo Найден на %%d:\Program Files ^(x86^)\LibreOffice\""
        goto :found_libreoffice
    )
    
    if exist "%%d:\LibreOffice\program\soffice.exe" (
        set "LIBREOFFICE_PATH=%%d:\LibreOffice\program\"
        echo Найден на %%d:\LibreOffice\"
        goto :found_libreoffice
    )
    
    if exist "%%d:\Apps\LibreOffice\program\soffice.exe" (
        set "LIBREOFFICE_PATH=%%d:\Apps\LibreOffice\program\"
        echo Найден на %%d:\Apps\LibreOffice\"
        goto :found_libreoffice
    )
    
    if exist "%%d:\Software\LibreOffice\program\soffice.exe" (
        set "LIBREOFFICE_PATH=%%d:\Software\LibreOffice\program\"
        echo Найден на %%d:\Software\LibreOffice\"
        goto :found_libreoffice
    )
)

:: Проверка в папке пользователя
if exist "%USERPROFILE%\AppData\Local\Programs\LibreOffice\program\soffice.exe" (
    set "LIBREOFFICE_PATH=%USERPROFILE%\AppData\Local\Programs\LibreOffice\program\"
    echo Найден в профиле пользователя
    goto :found_libreoffice
)

:: Проверка портативной версии в текущей папке
if exist "%~dp0LibreOffice\program\soffice.exe" (
    set "LIBREOFFICE_PATH=%~dp0LibreOffice\program\"
    echo Найдена портативная версия в текущей папке
    goto :found_libreoffice
)

:: Поиск через реестр Windows
echo "Поиск через реестр Windows..."
for /f "usebackq tokens=2*" %%a in (`reg query "HKEY_LOCAL_MACHINE\SOFTWARE\LibreOffice\UNO\InstallPath" /ve 2^>nul`) do (
    if exist "%%b\program\soffice.exe" (
        set "LIBREOFFICE_PATH=%%b\program\"
        echo "Найден через реестр: %%b"
        goto :found_libreoffice
    )
)

:: Поиск в реестре для 32-битных приложений на 64-битной системе
for /f "usebackq tokens=2*" %%a in (`reg query "HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\LibreOffice\UNO\InstallPath" /ve 2^>nul`) do (
    if exist "%%b\program\soffice.exe" (
        set "LIBREOFFICE_PATH=%%b\program\"
        echo "Найден через реестр (WOW64): %%b"
        goto :found_libreoffice
    )
)

:: Если не найден - предложить ручной ввод пути
echo LibreOffice не найден автоматически!
echo.
echo Попробуем найти вручную...
set /p "manual_path=Введите путь к папке LibreOffice program: "

if not "%manual_path%"=="" (
    if exist "%manual_path%\soffice.exe" (
        set "LIBREOFFICE_PATH=%manual_path%\"
        echo LibreOffice найден по указанному пути!
        goto :found_libreoffice
    ) else (
        echo По указанному пути LibreOffice не найден!
    )
)

echo.
echo LibreOffice не установлен или не найден!
echo.
echo Проверенные расположения:
echo    - Переменная PATH
echo    - C:\Program Files\LibreOffice\
echo    - C:\Program Files (x86)\LibreOffice\
echo    - D:\Program Files\LibreOffice\
echo    - D:\Program Files (x86)\LibreOffice\
echo    - D:\LibreOffice\
echo    - D:\Apps\LibreOffice\
echo    - D:\Software\LibreOffice\
echo    - %USERPROFILE%\AppData\Local\Programs\LibreOffice\
echo    - Портативная версия в текущей папке

echo.
echo Варианты решения:
echo 1. Скачать и установить LibreOffice:
echo    https://www.libreoffice.org/download/download/
echo.
echo 2. Если LibreOffice уже установлен, укажите путь вручную

echo.
echo Рекомендуемые пути установки:
echo    - C:\Program Files\LibreOffice\
echo    - D:\Program Files\LibreOffice\
echo.
echo Убедитесь, что путь содержит папку 'program' с файлом 'soffice.exe'
echo.
pause
exit /b 1

:found_libreoffice
echo LibreOffice найден: %LIBREOFFICE_PATH%

:: Установка переменных
set "DB_NAME=Журнал_выпуска_продукции"
set "DB_PATH=%~dp0%DB_NAME%.odb"
set "SQL_FILE=%~dp0create_tables.sql"
set "QUERIES_FILE=%~dp0queries.sql"
set "MACROS_FILE=%~dp0macros.bas"
set "GUIDE_FILE=%~dp0LibreOffice_Setup_Guide.md"
set "DETAILED_GUIDE_FILE=%~dp0ПОДРОБНАЯ_ИНСТРУКЦИЯ.md"

echo.
echo Путь к БД: %DB_PATH%

:: Меню выбора метода создания
echo.
echo Выберите действие:
echo 1. Создать все файлы и открыть LibreOffice (рекомендуется)
echo 2. Создать только SQL структуру
echo 3. Создать только макросы
echo 4. Создать краткое руководство
echo 5. Создать подробную инструкцию
echo 6. Создать всё и показать пошаговые инструкции
echo.
set /p choice="Введите номер (1-6): "

if "%choice%"=="1" goto :create_all
if "%choice%"=="2" goto :sql_only
if "%choice%"=="3" goto :macros_only
if "%choice%"=="4" goto :guide_only
if "%choice%"=="5" goto :detailed_guide_only
if "%choice%"=="6" goto :full_setup

echo Неверный выбор!
pause
exit /b 1

:create_all
echo.
echo Создание всех файлов...
call :create_sql_structure
call :create_queries
call :create_macros
call :create_guide
echo.
echo Открытие LibreOffice Base...
"%LIBREOFFICE_PATH%soffice.exe" --base
goto :success

:sql_only
echo.
echo Создание SQL структуры...
call :create_sql_structure
echo SQL файл создан: %SQL_FILE%
notepad "%SQL_FILE%"
goto :end

:macros_only
echo.
echo Создание макросов...
call :create_macros
echo Файл макросов создан: %MACROS_FILE%
notepad "%MACROS_FILE%"
goto :end

:guide_only
echo.
echo Создание краткого руководства...
call :create_guide
echo Краткое руководство создано: %GUIDE_FILE%
notepad "%GUIDE_FILE%"
goto :end

:detailed_guide_only
echo.
echo Создание подробной инструкции...
call :create_detailed_guide
echo Подробная инструкция создана: %DETAILED_GUIDE_FILE%
notepad "%DETAILED_GUIDE_FILE%"
goto :end

:full_setup
echo.
echo ПОЛНАЯ АВТОМАТИЧЕСКАЯ НАСТРОЙКА
echo.
call :create_sql_structure
call :create_queries
call :create_macros
call :create_guide
call :show_instructions
goto :success

:: Функция создания SQL структуры
:create_sql_structure
echo -- Создание структуры БД для LibreOffice Base > "%SQL_FILE%"
echo -- Электронный журнал выпуска продукции >> "%SQL_FILE%"
echo -- Автоматически сгенерировано %date% %time% >> "%SQL_FILE%"
echo. >> "%SQL_FILE%"
echo -- Таблица материалов >> "%SQL_FILE%"
echo CREATE TABLE МАТЕРИАЛЫ ^( >> "%SQL_FILE%"
echo     ID INTEGER IDENTITY PRIMARY KEY, >> "%SQL_FILE%"
echo     НАИМЕНОВАНИЕ VARCHAR^(200^) NOT NULL, >> "%SQL_FILE%"
echo     ИНВЕНТАРНЫЙ_НОМЕР VARCHAR^(50^) UNIQUE NOT NULL, >> "%SQL_FILE%"
echo     ЕДИНИЦА_ИЗМЕРЕНИЯ VARCHAR^(20^) NOT NULL, >> "%SQL_FILE%"
echo     ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ DECIMAL^(10,2^) DEFAULT 0, >> "%SQL_FILE%"
echo     ОПИСАНИЕ VARCHAR^(500^), >> "%SQL_FILE%"
echo     ДАТА_СОЗДАНИЯ TIMESTAMP DEFAULT CURRENT_TIMESTAMP >> "%SQL_FILE%"
echo ^); >> "%SQL_FILE%"
echo. >> "%SQL_FILE%"
echo -- Таблица журнала выпуска >> "%SQL_FILE%"
echo CREATE TABLE ЖУРНАЛ_ВЫПУСКА ^( >> "%SQL_FILE%"
echo     ID INTEGER IDENTITY PRIMARY KEY, >> "%SQL_FILE%"
echo     ДАТА_ВЫПУСКА DATE NOT NULL, >> "%SQL_FILE%"
echo     МАТЕРИАЛ_ID INTEGER NOT NULL, >> "%SQL_FILE%"
echo     КОЛИЧЕСТВО DECIMAL^(10,2^) NOT NULL, >> "%SQL_FILE%"
echo     ОБЩИЕ_ТРУДОЗАТРАТЫ DECIMAL^(10,2^), >> "%SQL_FILE%"
echo     ПРИМЕЧАНИЯ VARCHAR^(500^), >> "%SQL_FILE%"
echo     ДАТА_СОЗДАНИЯ TIMESTAMP DEFAULT CURRENT_TIMESTAMP, >> "%SQL_FILE%"
echo     FOREIGN KEY ^(МАТЕРИАЛ_ID^) REFERENCES МАТЕРИАЛЫ^(ID^) >> "%SQL_FILE%"
echo ^); >> "%SQL_FILE%"
echo. >> "%SQL_FILE%"
echo -- Создание индексов >> "%SQL_FILE%"
echo CREATE INDEX IDX_ЖУРНАЛ_ДАТА ON ЖУРНАЛ_ВЫПУСКА^(ДАТА_ВЫПУСКА^); >> "%SQL_FILE%"
echo CREATE INDEX IDX_ЖУРНАЛ_МАТЕРИАЛ ON ЖУРНАЛ_ВЫПУСКА^(МАТЕРИАЛ_ID^); >> "%SQL_FILE%"
echo CREATE INDEX IDX_МАТЕРИАЛЫ_НОМЕР ON МАТЕРИАЛЫ^(ИНВЕНТАРНЫЙ_НОМЕР^); >> "%SQL_FILE%"
echo. >> "%SQL_FILE%"
echo -- Тестовые данные >> "%SQL_FILE%"
echo INSERT INTO МАТЕРИАЛЫ ^(НАИМЕНОВАНИЕ, ИНВЕНТАРНЫЙ_НОМЕР, ЕДИНИЦА_ИЗМЕРЕНИЯ, ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ, ОПИСАНИЕ^) VALUES >> "%SQL_FILE%"
echo ^('Болт М8x20', 'БЛТ-001', 'шт', 0.5, 'Болт с метрической резьбой'^), >> "%SQL_FILE%"
echo ^('Гайка М8', 'ГК-001', 'шт', 0.3, 'Гайка метрическая'^), >> "%SQL_FILE%"
echo ^('Шайба 8мм', 'ШБ-001', 'шт', 0.1, 'Шайба плоская'^), >> "%SQL_FILE%"
echo ^('Лист стальной 2мм', 'ЛС-002', 'м²', 2.5, 'Лист стальной холоднокатаный'^), >> "%SQL_FILE%"
echo ^('Профиль 20x20', 'ПР-020', 'м', 1.8, 'Профиль квадратный'^); >> "%SQL_FILE%"
goto :eof

:: Функция создания запросов
:create_queries
echo -- Готовые запросы для LibreOffice Base > "%QUERIES_FILE%"
echo -- Электронный журнал выпуска продукции >> "%QUERIES_FILE%"
echo. >> "%QUERIES_FILE%"
echo -- 1. Информация о материале >> "%QUERIES_FILE%"
echo SELECT ID, НАИМЕНОВАНИЕ, ИНВЕНТАРНЫЙ_НОМЕР, ЕДИНИЦА_ИЗМЕРЕНИЯ, ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ, ОПИСАНИЕ >> "%QUERIES_FILE%"
echo FROM МАТЕРИАЛЫ WHERE ID = :МАТЕРИАЛ_ID; >> "%QUERIES_FILE%"
echo. >> "%QUERIES_FILE%"
echo -- 2. Журнал с автозаполнением >> "%QUERIES_FILE%"
echo SELECT j.ID, j.ДАТА_ВЫПУСКА, j.МАТЕРИАЛ_ID, m.НАИМЕНОВАНИЕ as НАЗВАНИЕ_МАТЕРИАЛА, >> "%QUERIES_FILE%"
echo        m.ИНВЕНТАРНЫЙ_НОМЕР, m.ЕДИНИЦА_ИЗМЕРЕНИЯ, j.КОЛИЧЕСТВО, >> "%QUERIES_FILE%"
echo        m.ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ, j.ОБЩИЕ_ТРУДОЗАТРАТЫ, j.ПРИМЕЧАНИЯ >> "%QUERIES_FILE%"
echo FROM ЖУРНАЛ_ВЫПУСКА j LEFT JOIN МАТЕРИАЛЫ m ON j.МАТЕРИАЛ_ID = m.ID >> "%QUERIES_FILE%"
echo ORDER BY j.ДАТА_ВЫПУСКА DESC, j.ID DESC; >> "%QUERIES_FILE%"
echo. >> "%QUERIES_FILE%"
echo -- 3. Ежемесячный отчет >> "%QUERIES_FILE%"
echo SELECT m.НАИМЕНОВАНИЕ, m.ИНВЕНТАРНЫЙ_НОМЕР, m.ЕДИНИЦА_ИЗМЕРЕНИЯ, >> "%QUERIES_FILE%"
echo        SUM^(j.КОЛИЧЕСТВО^) as ОБЩЕЕ_КОЛИЧЕСТВО, SUM^(j.ОБЩИЕ_ТРУДОЗАТРАТЫ^) as ОБЩИЕ_ТРУДОЗАТРАТЫ, >> "%QUЕРIES_FILE%"
echo        COUNT^(j.ID^) as КОЛИЧЕСТВО_ОПЕРАЦИЙ >> "%QUERIES_FILE%"
echo FROM ЖУРНАЛ_ВЫПУСКА j JOIN МАТЕРИАЛЫ m ON j.МАТЕРИАЛ_ID = m.ID >> "%QUERIES_FILE%"
echo WHERE YEAR^(j.ДАТА_ВЫПУСКА^) = :ГОД AND MONTH^(j.ДАТА_ВЫПУСКА^) = :МЕСЯЦ >> "%QUERIES_FILE%"
echo GROUP BY m.ID, m.НАИМЕНОВАНИЕ, m.ИНВЕНТАРНЫЙ_НОМЕР, m.ЕДИ
echo ORDER BY ОБЩИЕ_ТРУДОЗАТРАТЫ DESC; >> "%QUERIES_FILE%"
goto :eof

:: Функция создания макросов
:create_macros
echo REM Макросы для автозаполнения LibreOffice Base > "%MACROS_FILE%"
echo REM Электронный журнал выпуска продукции >> "%MACROS_FILE%"
echo. >> "%MACROS_FILE%"
echo REM Макрос автозаполнения при выборе материала >> "%MACROS_FILE%"
echo Sub OnMaterialChange^(oEvent^) >> "%MACROS_FILE%"
echo     Dim oForm As Object >> "%MACROS_FILE%"
echo     Dim oMaterialCombo As Object >> "%MACROS_FILE%"
echo     Dim oConnection As Object >> "%MACROS_FILE%"
echo     Dim oStatement As Object >> "%MACROS_FILE%"
echo     Dim oResultSet As Object >> "%MACROS_FILE%"
echo     Dim sSql As String >> "%MACROS_FILE%"
echo. >> "%MACROS_FILE%"
echo     oForm = oEvent.Source.getModel^(^).getParent^(^) >> "%MACROS_FILE%"
echo     oMaterialCombo = oForm.getByName^("МАТЕРИАЛ_ID"^) >> "%MACROS_FILE%"
echo. >> "%MACROS_FILE%"
echo     If oMaterialCombo.getCurrentValue^(^) ^<^> "" Then >> "%MACROS_FILE%"
echo         oConnection = oForm.getParent^(^).getConnection^(^) >> "%MACROS_FILE%"
echo         sSql = "SELECT ИНВЕНТАРНЫЙ_НОМЕР, ЕДИНИЦА_ИЗМЕРЕНИЯ, ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ FROM МАТЕРИАЛЫ WHERE ID = " ^& oMaterialCombo.getCurrentValue^(^) >> "%MACROS_FILE%"
echo         oStatement = oConnection.createStatement^(^) >> "%MACROS_FILE%"
echo         oResultSet = oStatement.executeQuery^(sSql^) >> "%MACROS_FILE%"
echo. >> "%MACROS_FILE%"
echo         If oResultSet.next^(^) Then >> "%MACROS_FILE%"
echo             oForm.getByName^("ИНВЕНТАРНЫЙ_НОМЕР"^).setString^(oResultSet.getString^(1^)^) >> "%MACROS_FILE%"
echo             oForm.getByName^("ЕДИНИЦА_ИЗМЕРЕНИЯ"^).setString^(oResultSet.getString^(2^)^) >> "%MACROS_FILE%"
echo             oForm.getByName^("ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ"^).setDouble^(oResultSet.getDouble^(3^)^) >> "%MACROS_FILE%"
echo             CalculateTotalLabor^(oForm^) >> "%MACROS_FILE%"
echo         End If >> "%MACROS_FILE%"
echo         oResultSet.close^(^) >> "%MACROS_FILE%"
echo         oStatement.close^(^) >> "%MACROS_FILE%"
echo     End If >> "%MACROS_FILE%"
echo End Sub >> "%MACROS_FILE%"
echo. >> "%MACROS_FILE%"
echo REM Пересчет трудозатрат >> "%MACROS_FILE%"
echo Sub CalculateTotalLabor^(oForm As Object^) >> "%MACROS_FILE%"
echo     Dim dQuantity As Double >> "%MACROS_FILE%"
echo     Dim dLaborPerUnit As Double >> "%MACROS_FILE%"
echo     dQuantity = oForm.getByName^("КОЛИЧЕСТВО"^).getDouble^(^) >> "%MACROS_FILE%"
echo     dLaborPerUnit = oForm.getByName^("ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ"^).getDouble^(^) >> "%MACROS_FILE%"
echo     oForm.getByName^("ОБЩИЕ_ТРУДОЗАТРАТЫ"^).setDouble^(dQuantity * dLaborPerUnit^) >> "%MACROS_FILE%"
echo End Sub >> "%MACROS_FILE%"
goto :eof

:: Функция создания руководства
:create_guide
echo # ПОШАГОВОЕ РУКОВОДСТВО > "%GUIDE_FILE%"
echo ## Электронный журнал выпуска продукции в LibreOffice Base >> "%GUIDE_FILE%"
echo. >> "%GUIDE_FILE%"
echo ### ШАГ 1: Создание базы данных >> "%GUIDE_FILE%"
echo 1. Откройте LibreOffice Base >> "%GUIDE_FILE%"
echo 2. Выберите "Создать базу данных" >> "%GUIDE_FILE%"
echo 3. Выберите "Встроенная база данных HSQLDB" >> "%GUIDE_FILE%"
echo 4. Сохраните как "Журнал_выпуска_продукции.odb" >> "%GUIDE_FILE%"
echo. >> "%GUIDE_FILE%"
echo ### ШАГ 2: Создание таблиц >> "%GUIDE_FILE%"
echo 1. Перейдите в режим SQL ^(Инструменты ^> SQL^) >> "%GUIDE_FILE%"
echo 2. Скопируйте содержимое файла create_tables.sql >> "%GUIDE_FILE%"
echo 3. Выполните команды по очереди >> "%GUIDE_FILE%"
echo. >> "%GUIDE_FILE%"
echo ### ШАГ 3: Создание форм >> "%GUIDE_FILE%"
echo 1. Создайте форму на основе таблицы ЖУРНАЛ_ВЫПУСКА >> "%GUIDE_FILE%"
echo 2. Добавьте комбо-бокс для выбора материала >> "%GUIDE_FILE%"
echo 3. Настройте автозаполнение через макросы >> "%GUIDE_FILE%"
echo. >> "%GUIDE_FILE%"
echo ### ШАГ 4: Установка макросов >> "%GUIDE_FILE%"
echo 1. Откройте редактор макросов ^(Сервис ^> Макросы^) >> "%GUIDE_FILE%"
echo 2. Создайте модуль "AutoFillModule" >> "%GUIDE_FILE%"
echo 3. Скопируйте код из файла macros.bas >> "%GUIDE_FILE%"
echo. >> "%GUIDE_FILE%"
echo ### ГОТОВО! Система готова к использованию. >> "%GUIDE_FILE%"
goto :eof

:: Функция показа инструкций
:show_instructions
echo.
echo 📋 ПОШАГОВЫЕ ИНСТРУКЦИИ:
echo.
echo 1️⃣ СОЗДАНИЕ БАЗЫ ДАННЫХ:
echo    • Откройте LibreOffice Base
echo    • Создайте новую БД (Embedded HSQLDB)
echo    • Сохраните как "Журнал_выпуска_продукции.odb"
echo.
echo 2️⃣ ИМПОРТ СТРУКТУРЫ:
echo    • Откройте режим SQL (F5 или Инструменты → SQL)
echo    • Скопируйте содержимое файла create_tables.sql
echo    • Выполните команды CREATE TABLE
echo.
echo 3️⃣ СОЗДАНИЕ ФОРМ:
echo    • Создайте форму журнала на основе таблицы ЖУРНАЛ_ВЫПУСКА
echo    • Добавьте комбо-бокс для выбора материала
echo    • Настройте поля для автозаполнения
echo.
echo 4️⃣ УСТАНОВКА МАКРОСОВ:
echo    • Откройте редактор макросов (Alt+F11)
echo    • Создайте модуль "AutoFillModule"
echo    • Скопируйте код из файла macros.bas
echo.
echo 5️⃣ НАСТРОЙКА СОБЫТИЙ:
echo    • В форме назначьте макрос OnMaterialChange
echo    • Привяжите к событию "При изменении" комбо-бокса
echo.
echo ⚡ АВТОМАТИЗАЦИЯ ГОТОВА!
echo.
pause
goto :eof

:success
echo.
echo 🎉 НАСТРОЙКА ЗАВЕРШЕНА УСПЕШНО!
echo.
echo 📂 Созданные файлы:
echo    • %SQL_FILE%
echo    • %QUERIES_FILE%
echo    • %MACROS_FILE%
echo    • %GUIDE_FILE%
echo.
echo 🔄 Следующие шаги:
echo 1. Откройте LibreOffice Base
echo 2. Создайте новую БД
echo 3. Импортируйте SQL структуру
echo 4. Настройте формы и макросы
echo.
echo 📖 Подробные инструкции в файле: %GUIDE_FILE%
echo.

:: Открытие файлов для удобства
set /p open_files="Открыть файлы для просмотра? (y/n): "
if /i "%open_files%"=="y" (
    start notepad "%SQL_FILE%"
    timeout /t 1 /nobreak >nul
    start notepad "%MACROS_FILE%"
    timeout /t 1 /nobreak >nul
    start notepad "%GUIDE_FILE%"
)

:end
echo.
echo ✅ Операция завершена!
echo 💡 Для запуска LibreOffice Base используйте:
echo    "%LIBREOFFICE_PATH%soffice.exe" --base