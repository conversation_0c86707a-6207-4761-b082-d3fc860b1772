@echo off
chcp 65001 >nul
title Setup AutoFill for Existing Database

echo.
echo ========================================
echo   SETUP AUTOFILL FOR EXISTING DATABASE
echo   Electronic Production Journal
echo ========================================
echo.

:: Check if database exists
set "DB_FILE=1.4 Electronic Production Journal.odb"
if not exist "%DB_FILE%" (
    set "DB_FILE=1.4 Elektronnyy zhurnal vypuska produktsii.odb"
)
if not exist "%DB_FILE%" (
    echo Database file not found!
    echo Looking for: 1.4 Electronic Production Journal.odb
    echo Please make sure the database file is in current folder
    pause
    exit /b 1
)

echo Database found: %DB_FILE%
echo.

:: Find LibreOffice
echo Searching for LibreOffice...
call :find_libreoffice

if "%LIBREOFFICE_PATH%"=="" (
    echo LibreOffice not found!
    echo Please install LibreOffice from official website
    pause
    exit /b 1
)

echo LibreOffice found: %LIBREOFFICE_PATH%
echo.

:: Main menu
:main_menu
echo ========================================
echo              MAIN MENU
echo ========================================
echo.
echo 1. OPEN DATABASE AND SETUP AUTOFILL
echo 2. CREATE MACROS ONLY
echo 3. CREATE QUERIES FOR REPORTS
echo 4. SHOW SETUP INSTRUCTIONS
echo 5. ANALYZE DATABASE STRUCTURE
echo 0. EXIT
echo.
set /p choice="Choose action (0-5): "

if "%choice%"=="1" goto :setup_existing_db
if "%choice%"=="2" goto :create_macros_only
if "%choice%"=="3" goto :create_queries_only
if "%choice%"=="4" goto :show_instructions
if "%choice%"=="5" goto :analyze_db_structure
if "%choice%"=="0" goto :exit

echo Invalid choice! Try again.
goto :main_menu

:: Setup existing database
:setup_existing_db
echo.
echo ========================================
echo    SETUP EXISTING DATABASE
echo ========================================
echo.

echo Setup plan:
echo    1. Open existing database
echo    2. Analyze table structure
echo    3. Create autofill macros
echo    4. Setup form with autofill
echo    5. Test functionality
echo.

echo Opening database...
start "" "%LIBREOFFICE_PATH%soffice.exe" --base "%CD%\%DB_FILE%"

echo.
echo Waiting for LibreOffice Base to load...
timeout /t 5 /nobreak >nul

echo.
echo ========================================
echo         STEP-BY-STEP ACTIONS
echo ========================================
echo.

echo STEP 1: ANALYZE DATABASE STRUCTURE
echo    • In LibreOffice Base go to "Tables" section
echo    • Find tables with materials and production journal
echo    • Remember exact table and field names
echo.
pause

echo STEP 2: CREATE MACROS
echo    • Press Alt+F11 (macro editor)
echo    • Create module "AutoFillModule"
echo    • Copy code from macros.bas file
echo.
call :create_macros
start notepad "macros.bas"
pause

echo STEP 3: SETUP FORM
echo    • Go to "Forms" section
echo    • Create new form or edit existing one
echo    • Data source: your production journal table
echo    • Add fields according to instructions
echo.
call :show_form_instructions
pause

echo STEP 4: SETUP EVENTS
echo    • For each field setup events:
echo      - Material selection field → OnMaterialChange
echo      - Material search field → OnMaterialNameChange
echo      - Quantity field → OnQuantityChange
echo.
pause

echo STEP 5: TESTING
echo    • Open created form
echo    • Test autofill functionality
echo    • Check labor cost calculations
echo.

goto :success

:: Create macros only
:create_macros_only
echo.
echo Creating macros for autofill...
call :create_macros
echo Macros file created: macros.bas
start notepad "macros.bas"
goto :main_menu

:create_macros
if exist "macros.bas" (
    echo Macros already exist
    goto :eof
)

echo REM Macros for LibreOffice Base autofill > "macros.bas"
echo REM Electronic Production Journal >> "macros.bas"
echo REM Adapted for existing database >> "macros.bas"
echo. >> "macros.bas"
echo REM IMPORTANT: Change table names according to your database! >> "macros.bas"
echo. >> "macros.bas"
echo REM Macro for autofill when material is selected by ID >> "macros.bas"
echo Sub OnMaterialChange(oEvent) >> "macros.bas"
echo     Dim oForm As Object >> "macros.bas"
echo     Dim oMaterialCombo As Object >> "macros.bas"
echo     Dim oConnection As Object >> "macros.bas"
echo     Dim oStatement As Object >> "macros.bas"
echo     Dim oResultSet As Object >> "macros.bas"
echo     Dim sSql As String >> "macros.bas"
echo. >> "macros.bas"
echo     oForm = oEvent.Source.getModel().getParent() >> "macros.bas"
echo     oMaterialCombo = oForm.getByName("MATERIAL_ID") >> "macros.bas"
echo. >> "macros.bas"
echo     If oMaterialCombo.getCurrentValue() ^<^> "" Then >> "macros.bas"
echo         oConnection = oForm.getParent().getConnection() >> "macros.bas"
echo         REM CHANGE table name to your materials table >> "macros.bas"
echo         sSql = "SELECT INVENTORY_NUMBER, UNIT, LABOR_COST_PER_UNIT FROM YOUR_MATERIALS_TABLE WHERE ID = " ^& oMaterialCombo.getCurrentValue() >> "macros.bas"
echo         oStatement = oConnection.createStatement() >> "macros.bas"
echo         oResultSet = oStatement.executeQuery(sSql) >> "macros.bas"
echo. >> "macros.bas"
echo         If oResultSet.next() Then >> "macros.bas"
echo             SetFieldValue(oForm, "INVENTORY_NUMBER", oResultSet.getString(1)) >> "macros.bas"
echo             SetFieldValue(oForm, "UNIT", oResultSet.getString(2)) >> "macros.bas"
echo             SetFieldValue(oForm, "LABOR_COST_PER_UNIT", oResultSet.getDouble(3)) >> "macros.bas"
echo             CalculateTotalLabor(oForm) >> "macros.bas"
echo         End If >> "macros.bas"
echo         oResultSet.close() >> "macros.bas"
echo         oStatement.close() >> "macros.bas"
echo     End If >> "macros.bas"
echo End Sub >> "macros.bas"
echo. >> "macros.bas"
echo REM Macro for autofill when material name is entered >> "macros.bas"
echo Sub OnMaterialNameChange(oEvent) >> "macros.bas"
echo     Dim oForm As Object >> "macros.bas"
echo     Dim oNameField As Object >> "macros.bas"
echo     Dim oConnection As Object >> "macros.bas"
echo     Dim oStatement As Object >> "macros.bas"
echo     Dim oResultSet As Object >> "macros.bas"
echo     Dim sSql As String >> "macros.bas"
echo     Dim sName As String >> "macros.bas"
echo. >> "macros.bas"
echo     oForm = oEvent.Source.getModel().getParent() >> "macros.bas"
echo     oNameField = oEvent.Source >> "macros.bas"
echo     sName = Trim(oNameField.getText()) >> "macros.bas"
echo. >> "macros.bas"
echo     If Len(sName) ^> 2 Then >> "macros.bas"
echo         oConnection = oForm.getParent().getConnection() >> "macros.bas"
echo         REM CHANGE table name to your materials table >> "macros.bas"
echo         sSql = "SELECT ID, INVENTORY_NUMBER, LABOR_COST_PER_UNIT FROM YOUR_MATERIALS_TABLE WHERE UPPER(NAME) LIKE UPPER('%" ^& sName ^& "%') ORDER BY NAME LIMIT 1" >> "macros.bas"
echo         oStatement = oConnection.createStatement() >> "macros.bas"
echo         oResultSet = oStatement.executeQuery(sSql) >> "macros.bas"
echo. >> "macros.bas"
echo         If oResultSet.next() Then >> "macros.bas"
echo             SetFieldValue(oForm, "MATERIAL_ID", oResultSet.getLong(1)) >> "macros.bas"
echo             SetFieldValue(oForm, "INVENTORY_NUMBER", oResultSet.getString(2)) >> "macros.bas"
echo             SetFieldValue(oForm, "LABOR_COST_PER_UNIT", oResultSet.getDouble(3)) >> "macros.bas"
echo             CalculateTotalLabor(oForm) >> "macros.bas"
echo         End If >> "macros.bas"
echo         oResultSet.close() >> "macros.bas"
echo         oStatement.close() >> "macros.bas"
echo     End If >> "macros.bas"
echo End Sub >> "macros.bas"
echo. >> "macros.bas"
echo REM Recalculate labor costs when quantity changes >> "macros.bas"
echo Sub OnQuantityChange(oEvent) >> "macros.bas"
echo     Dim oForm As Object >> "macros.bas"
echo     oForm = oEvent.Source.getModel().getParent() >> "macros.bas"
echo     CalculateTotalLabor(oForm) >> "macros.bas"
echo End Sub >> "macros.bas"
echo. >> "macros.bas"
echo REM Calculate total labor costs >> "macros.bas"
echo Sub CalculateTotalLabor(oForm As Object) >> "macros.bas"
echo     Dim dQuantity As Double >> "macros.bas"
echo     Dim dLaborPerUnit As Double >> "macros.bas"
echo. >> "macros.bas"
echo     On Error Resume Next >> "macros.bas"
echo     dQuantity = GetFieldValue(oForm, "QUANTITY") >> "macros.bas"
echo     dLaborPerUnit = GetFieldValue(oForm, "LABOR_COST_PER_UNIT") >> "macros.bas"
echo. >> "macros.bas"
echo     If dQuantity ^> 0 And dLaborPerUnit ^> 0 Then >> "macros.bas"
echo         SetFieldValue(oForm, "TOTAL_LABOR_COST", dQuantity * dLaborPerUnit) >> "macros.bas"
echo     End If >> "macros.bas"
echo End Sub >> "macros.bas"
echo. >> "macros.bas"
echo REM Helper functions >> "macros.bas"
echo Function GetFieldValue(oForm As Object, sFieldName As String) As Variant >> "macros.bas"
echo     Dim oField As Object >> "macros.bas"
echo     On Error Resume Next >> "macros.bas"
echo     oField = oForm.getByName(sFieldName) >> "macros.bas"
echo     If Not IsNull(oField) Then >> "macros.bas"
echo         If oField.supportsService("com.sun.star.form.component.NumericField") Then >> "macros.bas"
echo             GetFieldValue = oField.getValue() >> "macros.bas"
echo         Else >> "macros.bas"
echo             GetFieldValue = oField.getText() >> "macros.bas"
echo         End If >> "macros.bas"
echo     Else >> "macros.bas"
echo         GetFieldValue = 0 >> "macros.bas"
echo     End If >> "macros.bas"
echo End Function >> "macros.bas"
echo. >> "macros.bas"
echo Sub SetFieldValue(oForm As Object, sFieldName As String, vValue As Variant) >> "macros.bas"
echo     Dim oField As Object >> "macros.bas"
echo     On Error Resume Next >> "macros.bas"
echo     oField = oForm.getByName(sFieldName) >> "macros.bas"
echo     If Not IsNull(oField) Then >> "macros.bas"
echo         If oField.supportsService("com.sun.star.form.component.NumericField") Then >> "macros.bas"
echo             oField.setValue(vValue) >> "macros.bas"
echo         Else >> "macros.bas"
echo             oField.setText(CStr(vValue)) >> "macros.bas"
echo         End If >> "macros.bas"
echo     End If >> "macros.bas"
echo End Sub >> "macros.bas"

goto :eof

:: Create queries only
:create_queries_only
echo.
echo Creating queries for reports...
call :create_queries
echo Queries file created: queries_for_existing_db.sql
start notepad "queries_for_existing_db.sql"
goto :main_menu

:create_queries
echo -- Queries for reports - adapted for existing database > "queries_for_existing_db.sql"
echo -- IMPORTANT: Change table names according to your database! >> "queries_for_existing_db.sql"
echo. >> "queries_for_existing_db.sql"
echo -- 1. Search material by name >> "queries_for_existing_db.sql"
echo SELECT ID, INVENTORY_NUMBER, LABOR_COST_PER_UNIT >> "queries_for_existing_db.sql"
echo FROM YOUR_MATERIALS_TABLE >> "queries_for_existing_db.sql"
echo WHERE UPPER(NAME) LIKE UPPER(:SEARCH_NAME); >> "queries_for_existing_db.sql"
echo. >> "queries_for_existing_db.sql"
echo -- 2. Journal with autofill >> "queries_for_existing_db.sql"
echo SELECT j.ID, j.PRODUCTION_DATE, j.MATERIAL_ID, >> "queries_for_existing_db.sql"
echo        m.NAME as MATERIAL_NAME, >> "queries_for_existing_db.sql"
echo        m.INVENTORY_NUMBER, m.UNIT, >> "queries_for_existing_db.sql"
echo        j.QUANTITY, m.LABOR_COST_PER_UNIT, >> "queries_for_existing_db.sql"
echo        j.TOTAL_LABOR_COST, j.NOTES >> "queries_for_existing_db.sql"
echo FROM YOUR_JOURNAL_TABLE j >> "queries_for_existing_db.sql"
echo LEFT JOIN YOUR_MATERIALS_TABLE m ON j.MATERIAL_ID = m.ID >> "queries_for_existing_db.sql"
echo ORDER BY j.PRODUCTION_DATE DESC, j.ID DESC; >> "queries_for_existing_db.sql"
goto :eof

:: Show instructions
:show_instructions
echo.
echo Opening instructions...
call :create_existing_db_instructions
start notepad "INSTRUCTIONS_FOR_EXISTING_DB.md"
goto :main_menu

:create_existing_db_instructions
echo # SETUP AUTOFILL FOR EXISTING DATABASE > "INSTRUCTIONS_FOR_EXISTING_DB.md"
echo ## Step-by-step guide >> "INSTRUCTIONS_FOR_EXISTING_DB.md"
echo. >> "INSTRUCTIONS_FOR_EXISTING_DB.md"
echo ### STEP 1: Analyze database structure >> "INSTRUCTIONS_FOR_EXISTING_DB.md"
echo 1. Open your database in LibreOffice Base >> "INSTRUCTIONS_FOR_EXISTING_DB.md"
echo 2. Go to "Tables" section >> "INSTRUCTIONS_FOR_EXISTING_DB.md"
echo 3. Find tables with materials and journal >> "INSTRUCTIONS_FOR_EXISTING_DB.md"
echo 4. Write down exact table and field names >> "INSTRUCTIONS_FOR_EXISTING_DB.md"
echo. >> "INSTRUCTIONS_FOR_EXISTING_DB.md"
echo ### STEP 2: Adapt macros >> "INSTRUCTIONS_FOR_EXISTING_DB.md"
echo 1. Open macros.bas file >> "INSTRUCTIONS_FOR_EXISTING_DB.md"
echo 2. Replace "YOUR_MATERIALS_TABLE" with real name >> "INSTRUCTIONS_FOR_EXISTING_DB.md"
echo 3. Replace "YOUR_JOURNAL_TABLE" with real name >> "INSTRUCTIONS_FOR_EXISTING_DB.md"
echo 4. Check field names >> "INSTRUCTIONS_FOR_EXISTING_DB.md"
goto :eof

:show_form_instructions
echo.
echo FORM SETUP INSTRUCTIONS:
echo.
echo Form fields should have these names:
echo    • MATERIAL_ID - dropdown list of materials
echo    • MATERIAL_SEARCH - field for searching by name
echo    • INVENTORY_NUMBER - autofill field (read-only)
echo    • QUANTITY - input field for quantity
echo    • LABOR_COST_PER_UNIT - autofill field (read-only)
echo    • TOTAL_LABOR_COST - autofill field (read-only)
echo.
echo Field events:
echo    • MATERIAL_ID → On change → AutoFillModule.OnMaterialChange
echo    • MATERIAL_SEARCH → On change → AutoFillModule.OnMaterialNameChange
echo    • QUANTITY → On change → AutoFillModule.OnQuantityChange
echo.
goto :eof

:analyze_db_structure
echo.
echo ANALYZE EXISTING DATABASE STRUCTURE
echo.
echo To setup autofill you need to identify:
echo.
echo 1. MATERIALS TABLE:
echo    • Table name: _______________
echo    • ID field: _______________
echo    • Name field: _______________
echo    • Inventory number field: _______________
echo    • Labor cost field: _______________
echo.
echo 2. PRODUCTION JOURNAL TABLE:
echo    • Table name: _______________
echo    • Material ID field: _______________
echo    • Quantity field: _______________
echo    • Total labor cost field: _______________
echo.
echo Open your database and fill this information
echo Then adapt macros with your table names
echo.
pause
goto :main_menu

:find_libreoffice
set "LIBREOFFICE_PATH="
for %%d in (C D E F G H) do (
    if exist "%%d:\Program Files\LibreOffice\program\soffice.exe" (
        set "LIBREOFFICE_PATH=%%d:\Program Files\LibreOffice\program\"
        goto :found_libreoffice
    )
    if exist "%%d:\Program Files (x86)\LibreOffice\program\soffice.exe" (
        set "LIBREOFFICE_PATH=%%d:\Program Files (x86)\LibreOffice\program\"
        goto :found_libreoffice
    )
)
:found_libreoffice
goto :eof

:success
echo.
echo ========================================
echo         SETUP COMPLETED!
echo ========================================
echo.
echo Autofill setup for your existing database!
echo.
echo Don't forget to:
echo    • Adapt table names in macros
echo    • Check field names
echo    • Test autofill functionality
echo.
pause
goto :main_menu

:exit
echo.
echo Goodbye!
exit /b 0
