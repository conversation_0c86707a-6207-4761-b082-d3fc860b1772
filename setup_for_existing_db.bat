@echo off
chcp 65001 >nul
title Setup AutoFill for Your Existing Database

echo.
echo ==========================================
echo    SETUP AUTOFILL FOR EXISTING DATABASE
echo    Your Electronic Production Journal
echo ==========================================
echo.

:: Set LibreOffice path (found from previous run)
set "LIBREOFFICE_PATH=D:\Office\program\"
echo LibreOffice path: %LIBREOFFICE_PATH%

:: Check if your database exists
set "DB_FILE=1.4 Электронный журнал выпуска продукции.odb"
if not exist "%DB_FILE%" (
    echo Database file not found: %DB_FILE%
    echo Please make sure the database file is in current folder
    pause
    exit /b 1
)

echo Your database found: %DB_FILE%
echo.

:: Main menu
:main_menu
echo ==========================================
echo              MAIN MENU
echo ==========================================
echo.
echo 1. OPEN YOUR DATABASE AND SETUP AUTOFILL
echo 2. CREATE MACROS FOR YOUR DATABASE
echo 3. SHOW STEP-BY-STEP INSTRUCTIONS
echo 4. ANALYZE YOUR DATABASE STRUCTURE
echo 0. EXIT
echo.
set /p choice="Choose action (0-4): "

if "%choice%"=="1" goto :setup_existing_db
if "%choice%"=="2" goto :create_macros_only
if "%choice%"=="3" goto :show_instructions
if "%choice%"=="4" goto :analyze_structure
if "%choice%"=="0" goto :exit

echo Invalid choice! Try again.
goto :main_menu

:: Setup existing database
:setup_existing_db
echo.
echo ==========================================
echo    SETUP AUTOFILL FOR YOUR DATABASE
echo ==========================================
echo.

echo Opening your existing database...
start "" "%LIBREOFFICE_PATH%soffice.exe" --base "%CD%\%DB_FILE%"

echo.
echo Waiting for LibreOffice Base to load...
timeout /t 5 /nobreak >nul

echo.
echo ==========================================
echo       STEP-BY-STEP SETUP GUIDE
echo ==========================================
echo.

echo STEP 1: ANALYZE YOUR DATABASE STRUCTURE
echo ----------------------------------------
echo In LibreOffice Base:
echo • Go to "Tables" section
echo • Look at your table structure
echo • Find tables with:
echo   - Materials/Components (materials, nomenclature, etc.)
echo   - Production Journal (journal, operations, etc.)
echo • Write down exact table names and field names
echo.
pause

echo STEP 2: CREATE ADAPTED MACROS
echo ------------------------------
echo Creating macros adapted for your database...
call :create_macros
start notepad "macros_for_your_db.bas"
echo.
echo • Open the macros_for_your_db.bas file
echo • Replace placeholder table names with your real table names
echo • Check field names match your database
echo.
pause

echo STEP 3: INSTALL MACROS IN LIBREOFFICE
echo --------------------------------------
echo • In LibreOffice Base press Alt+F11 (macro editor)
echo • Create new module called "AutoFillModule"
echo • Copy the adapted code from macros_for_your_db.bas
echo • Save the module
echo.
pause

echo STEP 4: CREATE OR EDIT FORM
echo ----------------------------
echo • Go to "Forms" section in LibreOffice Base
echo • Create new form or edit existing form
echo • Base form on your production journal table
echo • Add these fields with exact names:
echo   - MATERIAL_ID (dropdown list of materials)
echo   - MATERIAL_SEARCH (text field for search)
echo   - INVENTORY_NUMBER (text field, read-only)
echo   - QUANTITY (numeric field)
echo   - LABOR_COST_PER_UNIT (numeric field, read-only)
echo   - TOTAL_LABOR_COST (numeric field, read-only)
echo.
pause

echo STEP 5: SETUP FIELD EVENTS
echo ---------------------------
echo For each field, setup events (right-click → Properties → Events):
echo • MATERIAL_ID field:
echo   - Event "When item changed" → AutoFillModule.OnMaterialChange
echo • MATERIAL_SEARCH field:
echo   - Event "When text changed" → AutoFillModule.OnMaterialNameChange
echo • QUANTITY field:
echo   - Event "When text changed" → AutoFillModule.OnQuantityChange
echo.
pause

echo STEP 6: TEST AUTOFILL FUNCTIONALITY
echo ------------------------------------
echo • Open your form
echo • Try selecting material from dropdown
echo • Try typing material name in search field
echo • Try entering quantity
echo • Check if fields auto-fill correctly
echo.

goto :success

:: Create macros only
:create_macros_only
echo.
echo Creating macros for your database...
call :create_macros
echo Macros file created: macros_for_your_db.bas
start notepad "macros_for_your_db.bas"
echo.
echo IMPORTANT: Edit the file to match your database structure!
echo Replace placeholder names with your real table and field names.
echo.
pause
goto :main_menu

:create_macros
echo Creating macros file for your database...

echo REM Macros for LibreOffice Base AutoFill > "macros_for_your_db.bas"
echo REM Electronic Production Journal >> "macros_for_your_db.bas"
echo REM Adapted for your existing database >> "macros_for_your_db.bas"
echo. >> "macros_for_your_db.bas"
echo REM ============================================ >> "macros_for_your_db.bas"
echo REM IMPORTANT: CUSTOMIZE FOR YOUR DATABASE >> "macros_for_your_db.bas"
echo REM ============================================ >> "macros_for_your_db.bas"
echo REM Replace these placeholder names with your real table names: >> "macros_for_your_db.bas"
echo REM - YOUR_MATERIALS_TABLE → your materials table name >> "macros_for_your_db.bas"
echo REM - YOUR_JOURNAL_TABLE → your production journal table name >> "macros_for_your_db.bas"
echo REM >> "macros_for_your_db.bas"
echo REM Check these field names match your database: >> "macros_for_your_db.bas"
echo REM - ID, NAME, INVENTORY_NUMBER, LABOR_COST_PER_UNIT >> "macros_for_your_db.bas"
echo REM - MATERIAL_ID, QUANTITY, TOTAL_LABOR_COST >> "macros_for_your_db.bas"
echo. >> "macros_for_your_db.bas"
echo REM Macro: Auto-fill when material is selected from dropdown >> "macros_for_your_db.bas"
echo Sub OnMaterialChange(oEvent) >> "macros_for_your_db.bas"
echo     Dim oForm As Object >> "macros_for_your_db.bas"
echo     Dim oMaterialCombo As Object >> "macros_for_your_db.bas"
echo     Dim oConnection As Object >> "macros_for_your_db.bas"
echo     Dim oStatement As Object >> "macros_for_your_db.bas"
echo     Dim oResultSet As Object >> "macros_for_your_db.bas"
echo     Dim sSql As String >> "macros_for_your_db.bas"
echo. >> "macros_for_your_db.bas"
echo     oForm = oEvent.Source.getModel().getParent() >> "macros_for_your_db.bas"
echo     oMaterialCombo = oForm.getByName("MATERIAL_ID") >> "macros_for_your_db.bas"
echo. >> "macros_for_your_db.bas"
echo     If oMaterialCombo.getCurrentValue() ^<^> "" Then >> "macros_for_your_db.bas"
echo         oConnection = oForm.getParent().getConnection() >> "macros_for_your_db.bas"
echo         REM CUSTOMIZE: Replace YOUR_MATERIALS_TABLE with your real table name >> "macros_for_your_db.bas"
echo         sSql = "SELECT INVENTORY_NUMBER, UNIT, LABOR_COST_PER_UNIT FROM YOUR_MATERIALS_TABLE WHERE ID = " ^& oMaterialCombo.getCurrentValue() >> "macros_for_your_db.bas"
echo         oStatement = oConnection.createStatement() >> "macros_for_your_db.bas"
echo         oResultSet = oStatement.executeQuery(sSql) >> "macros_for_your_db.bas"
echo. >> "macros_for_your_db.bas"
echo         If oResultSet.next() Then >> "macros_for_your_db.bas"
echo             SetFieldValue(oForm, "INVENTORY_NUMBER", oResultSet.getString(1)) >> "macros_for_your_db.bas"
echo             SetFieldValue(oForm, "UNIT", oResultSet.getString(2)) >> "macros_for_your_db.bas"
echo             SetFieldValue(oForm, "LABOR_COST_PER_UNIT", oResultSet.getDouble(3)) >> "macros_for_your_db.bas"
echo             CalculateTotalLabor(oForm) >> "macros_for_your_db.bas"
echo         End If >> "macros_for_your_db.bas"
echo         oResultSet.close() >> "macros_for_your_db.bas"
echo         oStatement.close() >> "macros_for_your_db.bas"
echo     End If >> "macros_for_your_db.bas"
echo End Sub >> "macros_for_your_db.bas"
echo. >> "macros_for_your_db.bas"
echo REM Macro: Auto-fill when material name is typed >> "macros_for_your_db.bas"
echo Sub OnMaterialNameChange(oEvent) >> "macros_for_your_db.bas"
echo     Dim oForm As Object >> "macros_for_your_db.bas"
echo     Dim oNameField As Object >> "macros_for_your_db.bas"
echo     Dim oConnection As Object >> "macros_for_your_db.bas"
echo     Dim oStatement As Object >> "macros_for_your_db.bas"
echo     Dim oResultSet As Object >> "macros_for_your_db.bas"
echo     Dim sSql As String >> "macros_for_your_db.bas"
echo     Dim sName As String >> "macros_for_your_db.bas"
echo. >> "macros_for_your_db.bas"
echo     oForm = oEvent.Source.getModel().getParent() >> "macros_for_your_db.bas"
echo     oNameField = oEvent.Source >> "macros_for_your_db.bas"
echo     sName = Trim(oNameField.getText()) >> "macros_for_your_db.bas"
echo. >> "macros_for_your_db.bas"
echo     If Len(sName) ^> 2 Then >> "macros_for_your_db.bas"
echo         oConnection = oForm.getParent().getConnection() >> "macros_for_your_db.bas"
echo         REM CUSTOMIZE: Replace YOUR_MATERIALS_TABLE with your real table name >> "macros_for_your_db.bas"
echo         sSql = "SELECT ID, INVENTORY_NUMBER, LABOR_COST_PER_UNIT FROM YOUR_MATERIALS_TABLE WHERE UPPER(NAME) LIKE UPPER('%" ^& sName ^& "%') ORDER BY NAME LIMIT 1" >> "macros_for_your_db.bas"
echo         oStatement = oConnection.createStatement() >> "macros_for_your_db.bas"
echo         oResultSet = oStatement.executeQuery(sSql) >> "macros_for_your_db.bas"
echo. >> "macros_for_your_db.bas"
echo         If oResultSet.next() Then >> "macros_for_your_db.bas"
echo             SetFieldValue(oForm, "MATERIAL_ID", oResultSet.getLong(1)) >> "macros_for_your_db.bas"
echo             SetFieldValue(oForm, "INVENTORY_NUMBER", oResultSet.getString(2)) >> "macros_for_your_db.bas"
echo             SetFieldValue(oForm, "LABOR_COST_PER_UNIT", oResultSet.getDouble(3)) >> "macros_for_your_db.bas"
echo             CalculateTotalLabor(oForm) >> "macros_for_your_db.bas"
echo         End If >> "macros_for_your_db.bas"
echo         oResultSet.close() >> "macros_for_your_db.bas"
echo         oStatement.close() >> "macros_for_your_db.bas"
echo     End If >> "macros_for_your_db.bas"
echo End Sub >> "macros_for_your_db.bas"
echo. >> "macros_for_your_db.bas"
echo REM Macro: Recalculate total when quantity changes >> "macros_for_your_db.bas"
echo Sub OnQuantityChange(oEvent) >> "macros_for_your_db.bas"
echo     Dim oForm As Object >> "macros_for_your_db.bas"
echo     oForm = oEvent.Source.getModel().getParent() >> "macros_for_your_db.bas"
echo     CalculateTotalLabor(oForm) >> "macros_for_your_db.bas"
echo End Sub >> "macros_for_your_db.bas"
echo. >> "macros_for_your_db.bas"
echo REM Helper: Calculate total labor cost >> "macros_for_your_db.bas"
echo Sub CalculateTotalLabor(oForm As Object) >> "macros_for_your_db.bas"
echo     Dim dQuantity As Double >> "macros_for_your_db.bas"
echo     Dim dLaborPerUnit As Double >> "macros_for_your_db.bas"
echo. >> "macros_for_your_db.bas"
echo     On Error Resume Next >> "macros_for_your_db.bas"
echo     dQuantity = GetFieldValue(oForm, "QUANTITY") >> "macros_for_your_db.bas"
echo     dLaborPerUnit = GetFieldValue(oForm, "LABOR_COST_PER_UNIT") >> "macros_for_your_db.bas"
echo. >> "macros_for_your_db.bas"
echo     If dQuantity ^> 0 And dLaborPerUnit ^> 0 Then >> "macros_for_your_db.bas"
echo         SetFieldValue(oForm, "TOTAL_LABOR_COST", dQuantity * dLaborPerUnit) >> "macros_for_your_db.bas"
echo     End If >> "macros_for_your_db.bas"
echo End Sub >> "macros_for_your_db.bas"
echo. >> "macros_for_your_db.bas"
echo REM Helper: Get field value safely >> "macros_for_your_db.bas"
echo Function GetFieldValue(oForm As Object, sFieldName As String) As Variant >> "macros_for_your_db.bas"
echo     Dim oField As Object >> "macros_for_your_db.bas"
echo     On Error Resume Next >> "macros_for_your_db.bas"
echo     oField = oForm.getByName(sFieldName) >> "macros_for_your_db.bas"
echo     If Not IsNull(oField) Then >> "macros_for_your_db.bas"
echo         If oField.supportsService("com.sun.star.form.component.NumericField") Then >> "macros_for_your_db.bas"
echo             GetFieldValue = oField.getValue() >> "macros_for_your_db.bas"
echo         Else >> "macros_for_your_db.bas"
echo             GetFieldValue = oField.getText() >> "macros_for_your_db.bas"
echo         End If >> "macros_for_your_db.bas"
echo     Else >> "macros_for_your_db.bas"
echo         GetFieldValue = 0 >> "macros_for_your_db.bas"
echo     End If >> "macros_for_your_db.bas"
echo End Function >> "macros_for_your_db.bas"
echo. >> "macros_for_your_db.bas"
echo REM Helper: Set field value safely >> "macros_for_your_db.bas"
echo Sub SetFieldValue(oForm As Object, sFieldName As String, vValue As Variant) >> "macros_for_your_db.bas"
echo     Dim oField As Object >> "macros_for_your_db.bas"
echo     On Error Resume Next >> "macros_for_your_db.bas"
echo     oField = oForm.getByName(sFieldName) >> "macros_for_your_db.bas"
echo     If Not IsNull(oField) Then >> "macros_for_your_db.bas"
echo         If oField.supportsService("com.sun.star.form.component.NumericField") Then >> "macros_for_your_db.bas"
echo             oField.setValue(vValue) >> "macros_for_your_db.bas"
echo         Else >> "macros_for_your_db.bas"
echo             oField.setText(CStr(vValue)) >> "macros_for_your_db.bas"
echo         End If >> "macros_for_your_db.bas"
echo     End If >> "macros_for_your_db.bas"
echo End Sub >> "macros_for_your_db.bas"

echo Macros file created successfully!
goto :eof

:: Show instructions
:show_instructions
echo.
echo ==========================================
echo         DETAILED INSTRUCTIONS
echo ==========================================
echo.
echo WHAT YOU NEED TO DO:
echo.
echo 1. ANALYZE YOUR DATABASE STRUCTURE
echo    • Open your database in LibreOffice Base
echo    • Go to Tables section
echo    • Identify your materials table and journal table
echo    • Note exact table names and field names
echo.
echo 2. CUSTOMIZE MACROS
echo    • Run option 2 to create macros file
echo    • Edit macros_for_your_db.bas
echo    • Replace YOUR_MATERIALS_TABLE with real name
echo    • Replace YOUR_JOURNAL_TABLE with real name
echo    • Check field names match your database
echo.
echo 3. INSTALL MACROS
echo    • In LibreOffice Base press Alt+F11
echo    • Create module "AutoFillModule"
echo    • Copy customized code from macros file
echo.
echo 4. CREATE FORM WITH AUTOFILL
echo    • Create form based on your journal table
echo    • Add fields with specific names for autofill
echo    • Setup events to call macros
echo.
echo 5. TEST FUNCTIONALITY
echo    • Test material selection
echo    • Test material search
echo    • Test quantity calculation
echo.
pause
goto :main_menu

:: Analyze structure
:analyze_structure
echo.
echo ==========================================
echo      ANALYZE YOUR DATABASE STRUCTURE
echo ==========================================
echo.
echo To setup autofill, you need to identify:
echo.
echo MATERIALS TABLE:
echo ----------------
echo Table name: _________________________
echo ID field: ___________________________
echo Name field: _________________________
echo Inventory number field: _____________
echo Labor cost field: ___________________
echo Unit field: _________________________
echo.
echo PRODUCTION JOURNAL TABLE:
echo -------------------------
echo Table name: _________________________
echo Material ID field: __________________
echo Quantity field: _____________________
echo Total labor cost field: _____________
echo Date field: _________________________
echo Notes field: ________________________
echo.
echo Write down this information and use it to customize the macros!
echo.
pause
goto :main_menu

:success
echo.
echo ==========================================
echo         SETUP COMPLETED!
echo ==========================================
echo.
echo Your autofill system is now configured!
echo.
echo WHAT YOU GET:
echo • Auto-fill inventory number when selecting material
echo • Auto-fill labor costs when selecting material
echo • Auto-calculate total costs when entering quantity
echo • Search materials by name with auto-fill
echo.
echo IMPORTANT REMINDERS:
echo • Make sure table names in macros match your database
echo • Test all functionality before using in production
echo • Create backup of your database before making changes
echo.
echo Time saved: Up to 80%% when entering production data!
echo.
pause
goto :main_menu

:exit
echo.
echo Thank you for using the autofill setup!
echo Your database: %DB_FILE%
echo LibreOffice: %LIBREOFFICE_PATH%
echo.
pause
exit /b 0
