@echo off
chcp 65001 >nul
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                 🔧 АВТОМАТИЧЕСКАЯ НАСТРОЙКА                  ║
echo ║            АВТОЗАПОЛНЕНИЯ LIBREOFFICE BASE                  ║
echo ║         Электронный журнал выпуска продукции                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: Проверка наличия LibreOffice
call :find_libreoffice
if "%LIBREOFFICE_PATH%"=="" (
    echo ❌ LibreOffice не найден!
    echo 💡 Установите LibreOffice с официального сайта:
    echo    https://www.libreoffice.org/download/download/
    pause
    exit /b 1
)

echo ✅ LibreOffice найден: %LIBREOFFICE_PATH%
echo.

:: Меню выбора действий
:menu
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      ВЫБЕРИТЕ ДЕЙСТВИЕ:                     ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║  1. 🚀 ПОЛНАЯ АВТОМАТИЧЕСКАЯ НАСТРОЙКА (рекомендуется)      ║
echo ║  2. 📊 Создать только структуру БД                          ║
echo ║  3. ⚙️  Создать только макросы                               ║
echo ║  4. 📋 Создать только инструкции                            ║
echo ║  5. 🔧 Открыть LibreOffice Base                             ║
echo ║  6. 📖 Показать пошаговую инструкцию                       ║
echo ║  0. ❌ Выход                                                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
set /p choice="Введите номер (0-6): "

if "%choice%"=="1" goto :full_setup
if "%choice%"=="2" goto :create_db_only
if "%choice%"=="3" goto :create_macros_only
if "%choice%"=="4" goto :create_instructions_only
if "%choice%"=="5" goto :open_libreoffice
if "%choice%"=="6" goto :show_instructions
if "%choice%"=="0" goto :exit
echo ❌ Неверный выбор!
goto :menu

:full_setup
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🚀 ПОЛНАЯ АВТОМАТИЧЕСКАЯ НАСТРОЙКА            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📝 Создание файлов...
call :create_all_files

echo.
echo 🔧 Запуск LibreOffice Base...
start "" "%LIBREOFFICE_PATH%soffice.exe" --base

echo.
echo ⏳ Ожидание загрузки LibreOffice...
timeout /t 3 /nobreak >nul

call :show_quick_guide
goto :success

:create_db_only
echo.
echo 📊 Создание структуры базы данных...
call :create_sql_structure
echo ✅ SQL файл создан: create_tables.sql
start notepad "create_tables.sql"
goto :menu

:create_macros_only
echo.
echo ⚙️ Создание макросов...
call :create_macros
echo ✅ Файл макросов создан: macros.bas
start notepad "macros.bas"
goto :menu

:create_instructions_only
echo.
echo 📋 Создание инструкций...
call :create_instructions
echo ✅ Инструкции созданы
goto :menu

:open_libreoffice
echo.
echo 🔧 Запуск LibreOffice Base...
start "" "%LIBREOFFICE_PATH%soffice.exe" --base
goto :menu

:show_instructions
call :show_detailed_instructions
goto :menu

:: ═══════════════════════════════════════════════════════════════
:: ФУНКЦИИ СОЗДАНИЯ ФАЙЛОВ
:: ═══════════════════════════════════════════════════════════════

:create_all_files
call :create_sql_structure
call :create_macros
call :create_queries
call :create_instructions
goto :eof

:create_sql_structure
if exist "create_tables.sql" (
    echo ✅ SQL структура уже существует
    goto :eof
)
echo 📝 Создание SQL структуры...
echo -- Создание структуры БД для LibreOffice Base > "create_tables.sql"
echo -- Электронный журнал выпуска продукции >> "create_tables.sql"
echo -- Автоматически сгенерировано %date% %time% >> "create_tables.sql"
echo. >> "create_tables.sql"
echo -- Таблица материалов >> "create_tables.sql"
echo CREATE TABLE МАТЕРИАЛЫ ^( >> "create_tables.sql"
echo     ID INTEGER IDENTITY PRIMARY KEY, >> "create_tables.sql"
echo     НАИМЕНОВАНИЕ VARCHAR^(200^) NOT NULL, >> "create_tables.sql"
echo     ИНВЕНТАРНЫЙ_НОМЕР VARCHAR^(50^) UNIQUE NOT NULL, >> "create_tables.sql"
echo     ЕДИНИЦА_ИЗМЕРЕНИЯ VARCHAR^(20^) NOT NULL, >> "create_tables.sql"
echo     ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ DECIMAL^(10,2^) DEFAULT 0, >> "create_tables.sql"
echo     ОПИСАНИЕ VARCHAR^(500^), >> "create_tables.sql"
echo     ДАТА_СОЗДАНИЯ TIMESTAMP DEFAULT CURRENT_TIMESTAMP >> "create_tables.sql"
echo ^); >> "create_tables.sql"
echo. >> "create_tables.sql"
echo -- Таблица журнала выпуска >> "create_tables.sql"
echo CREATE TABLE ЖУРНАЛ_ВЫПУСКА ^( >> "create_tables.sql"
echo     ID INTEGER IDENTITY PRIMARY KEY, >> "create_tables.sql"
echo     ДАТА_ВЫПУСКА DATE NOT NULL, >> "create_tables.sql"
echo     МАТЕРИАЛ_ID INTEGER NOT NULL, >> "create_tables.sql"
echo     КОЛИЧЕСТВО DECIMAL^(10,2^) NOT NULL, >> "create_tables.sql"
echo     ОБЩИЕ_ТРУДОЗАТРАТЫ DECIMAL^(10,2^), >> "create_tables.sql"
echo     ПРИМЕЧАНИЯ VARCHAR^(500^), >> "create_tables.sql"
echo     ДАТА_СОЗДАНИЯ TIMESTAMP DEFAULT CURRENT_TIMESTAMP, >> "create_tables.sql"
echo     FOREIGN KEY ^(МАТЕРИАЛ_ID^) REFERENCES МАТЕРИАЛЫ^(ID^) >> "create_tables.sql"
echo ^); >> "create_tables.sql"
echo. >> "create_tables.sql"
echo -- Тестовые данные >> "create_tables.sql"
echo INSERT INTO МАТЕРИАЛЫ ^(НАИМЕНОВАНИЕ, ИНВЕНТАРНЫЙ_НОМЕР, ЕДИНИЦА_ИЗМЕРЕНИЯ, ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ, ОПИСАНИЕ^) VALUES >> "create_tables.sql"
echo ^('Болт М8x20', 'БЛТ-001', 'шт', 0.5, 'Болт с метрической резьбой'^), >> "create_tables.sql"
echo ^('Гайка М8', 'ГК-001', 'шт', 0.3, 'Гайка метрическая'^), >> "create_tables.sql"
echo ^('Шайба 8мм', 'ШБ-001', 'шт', 0.1, 'Шайба плоская'^), >> "create_tables.sql"
echo ^('Лист стальной 2мм', 'ЛС-002', 'м²', 2.5, 'Лист стальной холоднокатаный'^), >> "create_tables.sql"
echo ^('Профиль 20x20', 'ПР-020', 'м', 1.8, 'Профиль квадратный'^); >> "create_tables.sql"
goto :eof

:create_macros
if exist "macros.bas" (
    echo ✅ Макросы уже существуют
    goto :eof
)
echo ⚙️ Создание макросов...
echo REM Макросы для автозаполнения LibreOffice Base > "macros.bas"
echo REM Электронный журнал выпуска продукции >> "macros.bas"
echo. >> "macros.bas"
echo REM Макрос автозаполнения при выборе материала по ID >> "macros.bas"
echo Sub OnMaterialChange^(oEvent^) >> "macros.bas"
echo     Dim oForm As Object >> "macros.bas"
echo     Dim oMaterialCombo As Object >> "macros.bas"
echo     Dim oConnection As Object >> "macros.bas"
echo     Dim oStatement As Object >> "macros.bas"
echo     Dim oResultSet As Object >> "macros.bas"
echo     Dim sSql As String >> "macros.bas"
echo. >> "macros.bas"
echo     oForm = oEvent.Source.getModel^(^).getParent^(^) >> "macros.bas"
echo     oMaterialCombo = oForm.getByName^("МАТЕРИАЛ_ID"^) >> "macros.bas"
echo. >> "macros.bas"
echo     If oMaterialCombo.getCurrentValue^(^) ^<^> "" Then >> "macros.bas"
echo         oConnection = oForm.getParent^(^).getConnection^(^) >> "macros.bas"
echo         sSql = "SELECT ИНВЕНТАРНЫЙ_НОМЕР, ЕДИНИЦА_ИЗМЕРЕНИЯ, ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ FROM МАТЕРИАЛЫ WHERE ID = " ^& oMaterialCombo.getCurrentValue^(^) >> "macros.bas"
echo         oStatement = oConnection.createStatement^(^) >> "macros.bas"
echo         oResultSet = oStatement.executeQuery^(sSql^) >> "macros.bas"
echo. >> "macros.bas"
echo         If oResultSet.next^(^) Then >> "macros.bas"
echo             SetFieldValue^(oForm, "ИНВЕНТАРНЫЙ_НОМЕР", oResultSet.getString^(1^)^) >> "macros.bas"
echo             SetFieldValue^(oForm, "ЕДИНИЦА_ИЗМЕРЕНИЯ", oResultSet.getString^(2^)^) >> "macros.bas"
echo             SetFieldValue^(oForm, "ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ", oResultSet.getDouble^(3^)^) >> "macros.bas"
echo             CalculateTotalLabor^(oForm^) >> "macros.bas"
echo         End If >> "macros.bas"
echo         oResultSet.close^(^) >> "macros.bas"
echo         oStatement.close^(^) >> "macros.bas"
echo     End If >> "macros.bas"
echo End Sub >> "macros.bas"
goto :eof

:create_queries
if exist "queries.sql" (
    echo ✅ Запросы уже существуют
    goto :eof
)
echo 📊 Создание запросов...
echo -- Готовые запросы для LibreOffice Base > "queries.sql"
echo -- Электронный журнал выпуска продукции >> "queries.sql"
echo. >> "queries.sql"
echo -- 1. Поиск материала по наименованию для автозаполнения >> "queries.sql"
echo SELECT ID, ИНВЕНТАРНЫЙ_НОМЕР, ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ >> "queries.sql"
echo FROM МАТЕРИАЛЫ >> "queries.sql"
echo WHERE UPPER^(НАИМЕНОВАНИЕ^) LIKE UPPER^(:НАИМЕНОВАНИЕ_ПОИСК^); >> "queries.sql"
echo. >> "queries.sql"
echo -- 2. Журнал с автозаполнением >> "queries.sql"
echo SELECT j.ID, j.ДАТА_ВЫПУСКА, j.МАТЕРИАЛ_ID, m.НАИМЕНОВАНИЕ as НАЗВАНИЕ_МАТЕРИАЛА, >> "queries.sql"
echo        m.ИНВЕНТАРНЫЙ_НОМЕР, m.ЕДИНИЦА_ИЗМЕРЕНИЯ, j.КОЛИЧЕСТВО, >> "queries.sql"
echo        m.ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ, j.ОБЩИЕ_ТРУДОЗАТРАТЫ, j.ПРИМЕЧАНИЯ >> "queries.sql"
echo FROM ЖУРНАЛ_ВЫПУСКА j LEFT JOIN МАТЕРИАЛЫ m ON j.МАТЕРИАЛ_ID = m.ID >> "queries.sql"
echo ORDER BY j.ДАТА_ВЫПУСКА DESC, j.ID DESC; >> "queries.sql"
goto :eof

:create_instructions
if exist "ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md" (
    echo ✅ Инструкции уже существуют
    goto :eof
)
echo 📋 Создание инструкций...
echo # 🔧 ПОШАГОВАЯ НАСТРОЙКА АВТОЗАПОЛНЕНИЯ > "ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md"
echo ## LibreOffice Base - Электронный журнал выпуска продукции >> "ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md"
echo. >> "ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md"
echo ### ШАГ 1: Создание базы данных >> "ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md"
echo 1. Откройте LibreOffice Base >> "ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md"
echo 2. Создайте новую БД ^(Embedded HSQLDB^) >> "ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md"
echo 3. Сохраните как "1.4 Электронный журнал выпуска продукции.odb" >> "ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md"
echo. >> "ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md"
echo ### ШАГ 2: Импорт структуры >> "ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md"
echo 1. Нажмите F5 ^(Инструменты → SQL^) >> "ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md"
echo 2. Скопируйте содержимое файла create_tables.sql >> "ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md"
echo 3. Выполните команды CREATE TABLE >> "ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md"
goto :eof

:: ═══════════════════════════════════════════════════════════════
:: ФУНКЦИИ ПОИСКА И ОТОБРАЖЕНИЯ
:: ═══════════════════════════════════════════════════════════════

:find_libreoffice
set "LIBREOFFICE_PATH="
for %%d in (C D E F G H) do (
    if exist "%%d:\Program Files\LibreOffice\program\soffice.exe" (
        set "LIBREOFFICE_PATH=%%d:\Program Files\LibreOffice\program\"
        goto :found_libreoffice
    )
    if exist "%%d:\Program Files (x86)\LibreOffice\program\soffice.exe" (
        set "LIBREOFFICE_PATH=%%d:\Program Files (x86)\LibreOffice\program\"
        goto :found_libreoffice
    )
)
:found_libreoffice
goto :eof

:show_quick_guide
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    📋 БЫСТРАЯ ИНСТРУКЦИЯ                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 1️⃣ В LibreOffice Base:
echo    • Создайте новую БД (Embedded HSQLDB)
echo    • Сохраните как "1.4 Электронный журнал выпуска продукции.odb"
echo.
echo 2️⃣ Импорт структуры:
echo    • Нажмите F5 (SQL)
echo    • Скопируйте код из create_tables.sql
echo    • Выполните команды
echo.
echo 3️⃣ Создание формы:
echo    • Создайте форму на основе ЖУРНАЛ_ВЫПУСКА
echo    • Добавьте поля с именами как в инструкции
echo    • Настройте события макросов
echo.
echo 4️⃣ Установка макросов:
echo    • Alt+F11 (редактор макросов)
echo    • Скопируйте код из macros.bas
echo.
echo 📖 Подробная инструкция: ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md
echo.
pause
goto :eof

:show_detailed_instructions
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                 📖 ПОДРОБНАЯ ИНСТРУКЦИЯ                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
if exist "ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md" (
    start notepad "ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md"
) else (
    call :create_instructions
    start notepad "ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md"
)
goto :eof

:success
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎉 НАСТРОЙКА ЗАВЕРШЕНА!                  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo ✅ Созданные файлы:
echo    • create_tables.sql - структура БД
echo    • macros.bas - макросы автозаполнения
echo    • queries.sql - готовые запросы
echo    • ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md - инструкции
echo.
echo 🔄 Следующие шаги:
echo    1. Создайте БД в LibreOffice Base
echo    2. Импортируйте SQL структуру
echo    3. Создайте форму с автозаполнением
echo    4. Установите макросы
echo.
pause
goto :menu

:exit
echo.
echo 👋 До свидания!
exit /b 0
