# 🚀 БЫСТРАЯ НАСТРОЙКА АВТОЗАПОЛНЕНИЯ ДЛЯ СУЩЕСТВУЮЩЕЙ БД

## 📋 Настройка автозаполнения для вашей готовой базы данных "1.4 Электронный журнал выпуска продукции.odb"

---

## ⚡ БЫСТРЫЙ СТАРТ (3 МИНУТЫ):

### **1. Запустите специальный скрипт:**
```bash
# Дважды кликните на файл:
НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ_СУЩЕСТВУЮЩЕЙ_БД.bat
```

### **2. Выберите "1. ОТКРЫТЬ БД И НАСТРОИТЬ АВТОЗАПОЛНЕНИЕ"**

---

## 🔍 ЧТО НУЖНО СДЕЛАТЬ:

### **Шаг 1: Анализ вашей БД (1 минута)**
1. **Откройте вашу базу данных** (скрипт сделает это автоматически)
2. **Перейдите в раздел "Таблицы"**
3. **Найдите таблицы:**
   - Таблица с материалами (например: "Материалы", "Номенклатура", "Справочник_материалов")
   - Таблица с журналом выпуска (например: "Журнал_выпуска", "Операции", "Выпуск_продукции")

### **Шаг 2: Адаптация макросов (1 минута)**
1. **Откройте файл `macros.bas`** (скрипт создаст его автоматически)
2. **Замените в коде:**
   - `ВАША_ТАБЛИЦА_МАТЕРИАЛОВ` → реальное название вашей таблицы материалов
   - `ВАША_ТАБЛИЦА_ЖУРНАЛА` → реальное название вашей таблицы журнала
3. **Проверьте названия полей** (ID, НАИМЕНОВАНИЕ, ИНВЕНТАРНЫЙ_НОМЕР и т.д.)

### **Шаг 3: Установка макросов (30 секунд)**
1. **В LibreOffice Base нажмите Alt+F11**
2. **Создайте модуль "AutoFillModule"**
3. **Скопируйте адаптированный код из macros.bas**

### **Шаг 4: Настройка формы (30 секунд)**
1. **Создайте или отредактируйте форму** на основе таблицы журнала
2. **Добавьте поля с именами:**
   - `МАТЕРИАЛ_ID` (выпадающий список)
   - `НАИМЕНОВАНИЕ_ПОИСК` (поле поиска)
   - `ИНВЕНТАРНЫЙ_НОМЕР` (автозаполнение)
   - `КОЛИЧЕСТВО` (ввод)
   - `ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ` (автозаполнение)
   - `ОБЩИЕ_ТРУДОЗАТРАТЫ` (автозаполнение)

3. **Настройте события:**
   - МАТЕРИАЛ_ID → OnMaterialChange
   - НАИМЕНОВАНИЕ_ПОИСК → OnMaterialNameChange
   - КОЛИЧЕСТВО → OnQuantityChange

---

## 🎯 РЕЗУЛЬТАТ:

✅ **Автозаполнение инвентарного номера** при выборе материала  
✅ **Автозаполнение трудозатрат** при выборе материала  
✅ **Автоматический расчет общих трудозатрат** при вводе количества  
✅ **Поиск материала по названию** с автозаполнением всех полей  

---

## 🔧 ПРИМЕР АДАПТАЦИИ МАКРОСА:

### **Если ваша таблица материалов называется "Справочник_материалов":**

**Было:**
```basic
sSql = "SELECT ИНВЕНТАРНЫЙ_НОМЕР, ЕДИНИЦА_ИЗМЕРЕНИЯ, ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ FROM ВАША_ТАБЛИЦА_МАТЕРИАЛОВ WHERE ID = " & oMaterialCombo.getCurrentValue()
```

**Стало:**
```basic
sSql = "SELECT ИНВЕНТАРНЫЙ_НОМЕР, ЕДИНИЦА_ИЗМЕРЕНИЯ, ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ FROM Справочник_материалов WHERE ID = " & oMaterialCombo.getCurrentValue()
```

---

## ⚠️ ВАЖНЫЕ МОМЕНТЫ:

### **Названия полей должны совпадать:**
- Если в вашей БД поле называется "Номер_материала" вместо "ИНВЕНТАРНЫЙ_НОМЕР", измените это в макросе
- Если поле трудозатрат называется по-другому, адаптируйте код

### **Типы полей:**
- ID материала должен быть числовым
- Поля трудозатрат должны быть числовыми (DECIMAL или DOUBLE)
- Поля текста должны быть текстовыми (VARCHAR)

### **Связи между таблицами:**
- Убедитесь, что в таблице журнала есть поле-ссылка на ID материала
- Проверьте, что связь настроена правильно

---

## 🧪 ТЕСТИРОВАНИЕ:

### **После настройки протестируйте:**
1. **Выберите материал из списка** → должны заполниться инвентарный номер и трудозатраты
2. **Введите название материала** → система должна найти и заполнить поля
3. **Введите количество** → должны рассчитаться общие трудозатраты

---

## 💡 ЕСЛИ ЧТО-ТО НЕ РАБОТАЕТ:

### **Проблема: Макросы не выполняются**
- Проверьте настройки безопасности макросов
- Разрешите выполнение макросов при открытии БД

### **Проблема: Автозаполнение не работает**
- Проверьте точность названий таблиц в макросах
- Убедитесь, что названия полей совпадают
- Проверьте, что события привязаны к правильным макросам

### **Проблема: Ошибки в SQL-запросах**
- Проверьте названия таблиц и полей
- Убедитесь, что поля существуют в БД

---

## 📞 БЫСТРАЯ ПОМОЩЬ:

**Запустите:** `НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ_СУЩЕСТВУЮЩЕЙ_БД.bat`  
**Выберите:** "5. АНАЛИЗ СТРУКТУРЫ СУЩЕСТВУЮЩЕЙ БД"  
**Заполните:** информацию о ваших таблицах и полях  

---

**🎉 Время настройки: 3-5 минут**  
**💡 Экономия времени при работе: до 80%**
