@echo off
chcp 65001 >nul
title Автоматическая настройка автозаполнения LibreOffice Base

echo.
echo ╔═══════════════════════════════════════════════════════════════════════════╗
echo ║                        🚀 АВТОМАТИЧЕСКАЯ НАСТРОЙКА                       ║
echo ║                      АВТОЗАПОЛНЕНИЯ LIBREOFFICE BASE                     ║
echo ║                   Электронный журнал выпуска продукции                   ║
echo ║                                                                           ║
echo ║  ✅ Автозаполнение инвентарного номера при выборе материала               ║
echo ║  ✅ Автозаполнение трудозатрат при выборе материала                       ║
echo ║  ✅ Автоматический расчет общих трудозатрат при вводе количества          ║
echo ║  ✅ Поиск материала по названию с автозаполнением всех полей              ║
echo ║  ✅ Готовые запросы для отчетов по расходу материалов                     ║
echo ╚═══════════════════════════════════════════════════════════════════════════╝
echo.

:: Проверка наличия всех необходимых файлов
echo 🔍 Проверка готовности к настройке...

set "missing_files="
if not exist "create_tables.sql" set "missing_files=%missing_files% create_tables.sql"
if not exist "macros.bas" set "missing_files=%missing_files% macros.bas"
if not exist "queries.sql" set "missing_files=%missing_files% queries.sql"

if not "%missing_files%"=="" (
    echo ⚠️  Отсутствуют файлы:%missing_files%
    echo 🔧 Создание недостающих файлов...
    call "АВТОМАТИЧЕСКАЯ_НАСТРОЙКА.bat"
    goto :end
)

echo ✅ Все файлы готовы!
echo.

:: Поиск LibreOffice
echo 🔍 Поиск LibreOffice...
call :find_libreoffice

if "%LIBREOFFICE_PATH%"=="" (
    echo ❌ LibreOffice не найден!
    echo.
    echo 💡 Установите LibreOffice с официального сайта:
    echo    https://www.libreoffice.org/download/download/
    echo.
    echo 📋 После установки запустите этот скрипт снова
    pause
    exit /b 1
)

echo ✅ LibreOffice найден: %LIBREOFFICE_PATH%
echo.

:: Главное меню
:main_menu
echo ╔═══════════════════════════════════════════════════════════════════════════╗
echo ║                              ГЛАВНОЕ МЕНЮ                                ║
echo ╠═══════════════════════════════════════════════════════════════════════════╣
echo ║                                                                           ║
echo ║  🚀 1. БЫСТРАЯ НАСТРОЙКА (5 минут)                                       ║
echo ║     • Создание БД + импорт структуры + настройка автозаполнения          ║
echo ║                                                                           ║
echo ║  📋 2. ПОШАГОВАЯ ИНСТРУКЦИЯ                                              ║
echo ║     • Подробное руководство с картинками                                 ║
echo ║                                                                           ║
echo ║  🔧 3. ОТКРЫТЬ LIBREOFFICE BASE                                          ║
echo ║     • Запуск программы для ручной настройки                              ║
echo ║                                                                           ║
echo ║  📁 4. ОТКРЫТЬ ФАЙЛЫ ДЛЯ ПРОСМОТРА                                       ║
echo ║     • SQL, макросы, запросы, инструкции                                  ║
echo ║                                                                           ║
echo ║  ❓ 5. ПОМОЩЬ И ДИАГНОСТИКА                                              ║
echo ║     • Решение проблем и проверка настроек                                ║
echo ║                                                                           ║
echo ║  ❌ 0. ВЫХОД                                                             ║
echo ║                                                                           ║
echo ╚═══════════════════════════════════════════════════════════════════════════╝
echo.
set /p choice="Выберите действие (0-5): "

if "%choice%"=="1" goto :quick_setup
if "%choice%"=="2" goto :show_instructions
if "%choice%"=="3" goto :open_libreoffice
if "%choice%"=="4" goto :open_files
if "%choice%"=="5" goto :help_diagnostics
if "%choice%"=="0" goto :exit

echo ❌ Неверный выбор! Попробуйте снова.
echo.
goto :main_menu

:: ═══════════════════════════════════════════════════════════════════════════
:: БЫСТРАЯ НАСТРОЙКА
:: ═══════════════════════════════════════════════════════════════════════════

:quick_setup
echo.
echo ╔═══════════════════════════════════════════════════════════════════════════╗
echo ║                          🚀 БЫСТРАЯ НАСТРОЙКА                            ║
echo ╚═══════════════════════════════════════════════════════════════════════════╝
echo.

echo 📋 Этапы настройки:
echo    1️⃣ Создание базы данных в LibreOffice Base
echo    2️⃣ Импорт структуры таблиц (SQL)
echo    3️⃣ Создание формы с автозаполнением
echo    4️⃣ Установка макросов
echo    5️⃣ Настройка событий
echo.

echo 🚀 Запуск LibreOffice Base...
start "" "%LIBREOFFICE_PATH%soffice.exe" --base

echo.
echo ⏳ Ожидание загрузки LibreOffice Base...
timeout /t 5 /nobreak >nul

echo.
echo ╔═══════════════════════════════════════════════════════════════════════════╗
echo ║                        📋 ПОШАГОВЫЕ ДЕЙСТВИЯ                             ║
echo ╚═══════════════════════════════════════════════════════════════════════════╝
echo.

echo 1️⃣ СОЗДАНИЕ БАЗЫ ДАННЫХ:
echo    • В LibreOffice Base выберите "Создать базу данных"
echo    • Тип: "Встроенная база данных HSQLDB"
echo    • Сохраните как: "1.4 Электронный журнал выпуска продукции.odb"
echo.
pause

echo 2️⃣ ИМПОРТ СТРУКТУРЫ ТАБЛИЦ:
echo    • В LibreOffice Base нажмите F5 (или Инструменты → SQL)
echo    • Откройте файл create_tables.sql в блокноте
start notepad "create_tables.sql"
echo    • Скопируйте весь код и вставьте в окно SQL
echo    • Нажмите "Выполнить"
echo.
pause

echo 3️⃣ СОЗДАНИЕ ФОРМЫ:
echo    • Перейдите в раздел "Формы"
echo    • Создайте форму в режиме конструктора
echo    • Источник данных: таблица ЖУРНАЛ_ВЫПУСКА
echo    • Добавьте поля согласно инструкции
echo.
start notepad "ИНСТРУКЦИЯ_ПО_СОЗДАНИЮ_ФОРМ.md"
pause

echo 4️⃣ УСТАНОВКА МАКРОСОВ:
echo    • В LibreOffice Base нажмите Alt+F11
echo    • Создайте модуль "AutoFillModule"
echo    • Скопируйте код из файла macros.bas
start notepad "macros.bas"
echo.
pause

echo 5️⃣ НАСТРОЙКА СОБЫТИЙ:
echo    • В форме настройте события для полей:
echo      - МАТЕРИАЛ_ID → OnMaterialChange
echo      - НАИМЕНОВАНИЕ_ПОИСК → OnMaterialNameChange  
echo      - КОЛИЧЕСТВО → OnQuantityChange
echo.
pause

echo ✅ НАСТРОЙКА ЗАВЕРШЕНА!
echo.
echo 🧪 ТЕСТИРОВАНИЕ:
echo    • Откройте созданную форму
echo    • Выберите материал из списка
echo    • Проверьте автозаполнение полей
echo    • Введите количество и проверьте расчет
echo.

goto :success

:: ═══════════════════════════════════════════════════════════════════════════
:: ДРУГИЕ ФУНКЦИИ
:: ═══════════════════════════════════════════════════════════════════════════

:show_instructions
echo.
echo 📋 Открытие подробной инструкции...
if exist "ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md" (
    start notepad "ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md"
) else (
    echo ❌ Файл инструкции не найден!
    echo 🔧 Создание инструкции...
    call "АВТОМАТИЧЕСКАЯ_НАСТРОЙКА.bat"
)
goto :main_menu

:open_libreoffice
echo.
echo 🔧 Запуск LibreOffice Base...
start "" "%LIBREOFFICE_PATH%soffice.exe" --base
goto :main_menu

:open_files
echo.
echo 📁 Открытие файлов для просмотра...
if exist "create_tables.sql" start notepad "create_tables.sql"
timeout /t 1 /nobreak >nul
if exist "macros.bas" start notepad "macros.bas"
timeout /t 1 /nobreak >nul
if exist "queries.sql" start notepad "queries.sql"
timeout /t 1 /nobreak >nul
if exist "ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md" start notepad "ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md"
goto :main_menu

:help_diagnostics
echo.
echo ╔═══════════════════════════════════════════════════════════════════════════╗
echo ║                        ❓ ПОМОЩЬ И ДИАГНОСТИКА                           ║
echo ╚═══════════════════════════════════════════════════════════════════════════╝
echo.

echo 🔍 ПРОВЕРКА СИСТЕМЫ:
echo.
echo LibreOffice: %LIBREOFFICE_PATH%
if exist "%LIBREOFFICE_PATH%soffice.exe" (
    echo ✅ LibreOffice найден и готов к работе
) else (
    echo ❌ LibreOffice не найден или поврежден
)
echo.

echo 📁 ПРОВЕРКА ФАЙЛОВ:
if exist "create_tables.sql" (echo ✅ create_tables.sql) else (echo ❌ create_tables.sql)
if exist "macros.bas" (echo ✅ macros.bas) else (echo ❌ macros.bas)
if exist "queries.sql" (echo ✅ queries.sql) else (echo ❌ queries.sql)
if exist "ПОШАГОВАЯ_НАСТРОЙКА_АВТОЗАПОЛНЕНИЯ.md" (echo ✅ Инструкции) else (echo ❌ Инструкции)
echo.

echo 💡 ЧАСТЫЕ ПРОБЛЕМЫ И РЕШЕНИЯ:
echo.
echo ❓ Макросы не работают:
echo    • Проверьте настройки безопасности макросов
echo    • Разрешите выполнение макросов при открытии БД
echo.
echo ❓ Автозаполнение не работает:
echo    • Проверьте имена полей в форме
echo    • Убедитесь, что события привязаны к макросам
echo.
echo ❓ Ошибки при импорте SQL:
echo    • Выполняйте команды по одной
echo    • Проверьте кодировку файла (UTF-8)
echo.

pause
goto :main_menu

:: ═══════════════════════════════════════════════════════════════════════════
:: ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ
:: ═══════════════════════════════════════════════════════════════════════════

:find_libreoffice
set "LIBREOFFICE_PATH="
for %%d in (C D E F G H) do (
    if exist "%%d:\Program Files\LibreOffice\program\soffice.exe" (
        set "LIBREOFFICE_PATH=%%d:\Program Files\LibreOffice\program\"
        goto :found_libreoffice
    )
    if exist "%%d:\Program Files (x86)\LibreOffice\program\soffice.exe" (
        set "LIBREOFFICE_PATH=%%d:\Program Files (x86)\LibreOffice\program\"
        goto :found_libreoffice
    )
)
:found_libreoffice
goto :eof

:success
echo.
echo ╔═══════════════════════════════════════════════════════════════════════════╗
echo ║                          🎉 УСПЕШНО ЗАВЕРШЕНО!                           ║
echo ╚═══════════════════════════════════════════════════════════════════════════╝
echo.
echo ✅ Автозаполнение настроено и готово к работе!
echo.
echo 🎯 РЕЗУЛЬТАТ:
echo    • Автозаполнение инвентарного номера при выборе материала
echo    • Автозаполнение трудозатрат при выборе материала  
echo    • Автоматический расчет общих трудозатрат
echo    • Поиск материала по названию с автозаполнением
echo.
echo 💡 ЭКОНОМИЯ ВРЕМЕНИ: до 80% при вводе данных!
echo.
pause
goto :main_menu

:exit
echo.
echo 👋 Спасибо за использование автоматической настройки!
echo 💡 Если возникнут вопросы, запустите скрипт снова и выберите "Помощь"
echo.
pause
exit /b 0

:end
pause
