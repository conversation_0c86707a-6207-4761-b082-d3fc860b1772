НАСТРОЙКА АВТОЗАПОЛНЕНИЯ ДЛЯ СУЩЕСТВУЮЩЕЙ БАЗЫ ДАННЫХ
========================================================

БЫСТРЫЙ СТАРТ (3 МИНУТЫ):
=========================

1. Запустите файл: setup_existing_db.bat
2. Выберите "1. OPEN DATABASE AND SETUP AUTOFILL"
3. Следуйте инструкциям на экране

ЧТО БУДЕТ НАСТРОЕНО:
===================

✓ Автозаполнение инвентарного номера при выборе материала
✓ Автозаполнение трудозатрат при выборе материала  
✓ Автоматический расчет общих трудозатрат при вводе количества
✓ Поиск материала по названию с автозаполнением всех полей

ПОШАГОВАЯ ИНСТРУКЦИЯ:
====================

ШАГ 1: АНАЛИЗ СТРУКТУРЫ ВАШЕЙ БД
-------------------------------
1. Откройте вашу базу данных в LibreOffice Base
2. Перейдите в раздел "Таблицы"
3. Найдите таблицы:
   - Таблица с материалами (например: "Материалы", "Номенклатура")
   - Таблица с журналом выпуска (например: "Журнал_выпуска", "Операции")
4. Запишите точные названия таблиц и полей

ШАГ 2: АДАПТАЦИЯ МАКРОСОВ
-------------------------
1. Откройте файл macros.bas (создается автоматически)
2. Замените в коде:
   - "YOUR_MATERIALS_TABLE" → реальное название вашей таблицы материалов
   - "YOUR_JOURNAL_TABLE" → реальное название вашей таблицы журнала
3. Проверьте названия полей:
   - ID, NAME, INVENTORY_NUMBER, LABOR_COST_PER_UNIT и т.д.

ШАГ 3: УСТАНОВКА МАКРОСОВ
-------------------------
1. В LibreOffice Base нажмите Alt+F11
2. Создайте модуль "AutoFillModule"
3. Скопируйте адаптированный код из macros.bas

ШАГ 4: НАСТРОЙКА ФОРМЫ
----------------------
1. Создайте или отредактируйте форму на основе таблицы журнала
2. Добавьте поля с именами:
   - MATERIAL_ID (выпадающий список)
   - MATERIAL_SEARCH (поле поиска)
   - INVENTORY_NUMBER (автозаполнение)
   - QUANTITY (ввод)
   - LABOR_COST_PER_UNIT (автозаполнение)
   - TOTAL_LABOR_COST (автозаполнение)

3. Настройте события:
   - MATERIAL_ID → OnMaterialChange
   - MATERIAL_SEARCH → OnMaterialNameChange
   - QUANTITY → OnQuantityChange

ШАГ 5: ТЕСТИРОВАНИЕ
-------------------
1. Откройте созданную форму
2. Выберите материал из списка
3. Проверьте автозаполнение полей
4. Введите количество и проверьте расчет трудозатрат

ПРИМЕР АДАПТАЦИИ МАКРОСА:
========================

Если ваша таблица материалов называется "Справочник_материалов":

БЫЛО:
sSql = "SELECT INVENTORY_NUMBER, UNIT, LABOR_COST_PER_UNIT FROM YOUR_MATERIALS_TABLE WHERE ID = " & oMaterialCombo.getCurrentValue()

СТАЛО:
sSql = "SELECT INVENTORY_NUMBER, UNIT, LABOR_COST_PER_UNIT FROM Справочник_материалов WHERE ID = " & oMaterialCombo.getCurrentValue()

ВАЖНЫЕ МОМЕНТЫ:
===============

• Названия полей должны точно совпадать с вашей БД
• ID материала должен быть числовым полем
• Поля трудозатрат должны быть числовыми (DECIMAL или DOUBLE)
• Убедитесь, что связь между таблицами настроена правильно

ЕСЛИ ЧТО-ТО НЕ РАБОТАЕТ:
=======================

Проблема: Макросы не выполняются
Решение: Проверьте настройки безопасности макросов, разрешите выполнение

Проблема: Автозаполнение не работает
Решение: Проверьте названия таблиц и полей в макросах

Проблема: Ошибки в SQL-запросах
Решение: Убедитесь, что названия таблиц и полей существуют в БД

РЕЗУЛЬТАТ:
==========

После настройки вы получите:
✓ Полностью автоматизированную форму ввода данных
✓ Экономию времени до 80% при заполнении журнала
✓ Исключение ошибок при вводе инвентарных номеров
✓ Автоматический расчет трудозатрат
✓ Удобный поиск материалов по названию

Время настройки: 3-5 минут
Готовность к работе: сразу после настройки
