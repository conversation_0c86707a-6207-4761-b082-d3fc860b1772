
# 🔧 АВТОМАТИЧЕСКАЯ НАСТРОЙКА ФОРМ LIBREOFFICE BASE

## 📋 ИНСТРУКЦИЯ ПО ИМПОРТУ ФОРМЫ:

### Шаг 1: Подготовка
1. Убедитесь, что база данных создана и таблицы импортированы
2. Убедитесь, что макросы загружены в LibreOffice Basic

### Шаг 2: Создание формы вручную (рекомендуется)
Поскольку LibreOffice Base не поддерживает прямой импорт XML-форм,
создайте форму вручную по следующему шаблону:

#### 🔹 Настройки формы:
- **Имя формы:** Журнал_выпуска_форма
- **Источник данных:** таблица ЖУРНАЛ_ВЫПУСКА
- **Тип:** Форма данных

#### 🔹 Поля формы:

**1. Дата выпуска**
- Тип: Поле даты
- Имя: ДАТА_ВЫПУСКА
- Связанное поле: ДАТА_ВЫПУСКА

**2. Выбор материала**
- Тип: Список (Combo Box)
- Имя: МАТЕРИАЛ_ID
- Связанное поле: МАТЕРИАЛ_ID
- Источник списка: SELECT ID, НАИМЕНОВАНИЕ FROM МАТЕРИАЛЫ ORDER BY НАИМЕНОВАНИЕ
- Связанный столбец: 0
- Отображаемый столбец: 1
- Событие "При изменении": AutoFillModule.OnMaterialChange

**3. Поиск по названию**
- Тип: Текстовое поле
- Имя: НАИМЕНОВАНИЕ_ПОИСК
- Связанное поле: (не связывать)
- Событие "При изменении": AutoFillModule.OnMaterialNameChange

**4. Инвентарный номер**
- Тип: Текстовое поле
- Имя: ИНВЕНТАРНЫЙ_НОМЕР
- Только для чтения: Да

**5. Количество**
- Тип: Числовое поле
- Имя: КОЛИЧЕСТВО
- Связанное поле: КОЛИЧЕСТВО
- Событие "При изменении": AutoFillModule.OnQuantityChange

**6. Трудозатраты на единицу**
- Тип: Числовое поле
- Имя: ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ
- Только для чтения: Да

**7. Общие трудозатраты**
- Тип: Числовое поле
- Имя: ОБЩИЕ_ТРУДОЗАТРАТЫ
- Связанное поле: ОБЩИЕ_ТРУДОЗАТРАТЫ
- Только для чтения: Да

**8. Примечания**
- Тип: Многострочное текстовое поле
- Имя: ПРИМЕЧАНИЯ
- Связанное поле: ПРИМЕЧАНИЯ

### Шаг 3: Проверка работы
1. Откройте форму
2. Выберите материал из списка
3. Проверьте автозаполнение полей
4. Введите количество и проверьте расчет трудозатрат

## ⚠️ ВАЖНО:
- Имена полей должны точно соответствовать указанным
- Макросы должны быть загружены и разрешены
- События должны быть правильно привязаны к макросам
