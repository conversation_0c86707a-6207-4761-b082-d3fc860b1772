# 🔧 НАСТРОЙКА АВТОЗАПОЛНЕНИЯ И ОТЧЁТОВ
## Пошаговое руководство по настройке связей и отчётов

---

## 📋 1. НАСТРОЙКА АВТОЗАПОЛНЕНИЯ ПОЛЕЙ

### **Шаг 1: Создание формы для ввода данных**

1. **Откройте LibreOffice Base** с вашей базой данных
2. **Перейдите в раздел "Формы"**
3. **Создайте новую форму** через "Мастер форм"
4. **Выберите таблицу** `ЖУРНАЛ_ВЫПУСКА` как основную
5. **Добавьте поля:**
   - `ДАТА_ВЫПУСКА` (поле даты)
   - `МАТЕРИАЛ_ID` (выпадающий список)
   - `НАИМЕНОВАНИЕ` (текстовое поле для ввода названия)
   - `ИНВЕНТАРНЫЙ_НОМЕР` (только для чтения)
   - `КОЛИЧЕСТВО` (числовое поле)
   - `ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ` (только для чтения)
   - `ОБЩИЕ_ТРУДОЗАТРАТЫ` (только для чтения)
   - `ПРИМЕЧАНИЯ` (текстовое поле)

### **Шаг 2: Настройка выпадающего списка материалов**

1. **Выберите поле** `МАТЕРИАЛ_ID`
2. **Откройте свойства поля** (правый клик → Свойства)
3. **На вкладке "Данные":**
   - Тип содержимого: `Список`
   - Источник списка: `SELECT ID, НАИМЕНОВАНИЕ FROM МАТЕРИАЛЫ ORDER BY НАИМЕНОВАНИЕ`
   - Связанное поле: `0` (ID)
   - Отображаемое поле: `1` (НАИМЕНОВАНИЕ)

### **Шаг 3: Привязка макросов к событиям**

1. **Для поля МАТЕРИАЛ_ID:**
   - Откройте свойства → вкладка "События"
   - Событие "При изменении": `OnMaterialChange`

2. **Для поля НАИМЕНОВАНИЕ:**
   - Событие "При изменении": `OnMaterialNameChange`

3. **Для поля КОЛИЧЕСТВО:**
   - Событие "При изменении": `OnQuantityChange`

---

## 📊 2. СОЗДАНИЕ ОТЧЁТА ПО РАСХОДУ МАТЕРИАЛОВ

### **Шаг 1: Создание запроса для отчёта**

1. **Перейдите в раздел "Запросы"**
2. **Создайте новый запрос** → "Создать запрос в режиме SQL"
3. **Вставьте следующий SQL-код:**

```sql
SELECT 
    m.ИНВЕНТАРНЫЙ_НОМЕР as "Инвентарный номер",
    m.НАИМЕНОВАНИЕ as "Наименование материала", 
    m.ЕДИНИЦА_ИЗМЕРЕНИЯ as "Ед. изм.",
    SUM(j.КОЛИЧЕСТВО) as "Количество за месяц",
    SUM(j.ОБЩИЕ_ТРУДОЗАТРАТЫ) as "Общие трудозатраты",
    COUNT(j.ID) as "Количество операций"
FROM ЖУРНАЛ_ВЫПУСКА j 
JOIN МАТЕРИАЛЫ m ON j.МАТЕРИАЛ_ID = m.ID 
WHERE YEAR(j.ДАТА_ВЫПУСКА) = :ГОД AND MONTH(j.ДАТА_ВЫПУСКА) = :МЕСЯЦ 
GROUP BY m.ID, m.ИНВЕНТАРНЫЙ_НОМЕР, m.НАИМЕНОВАНИЕ, m.ЕДИНИЦА_ИЗМЕРЕНИЯ
ORDER BY SUM(j.КОЛИЧЕСТВО) DESC
```

4. **Сохраните запрос** под именем `Отчёт_по_материалам_за_месяц`

### **Шаг 2: Создание отчёта с фильтром по дате**

1. **Создайте ещё один запрос:**

```sql
SELECT 
    m.ИНВЕНТАРНЫЙ_НОМЕР as "Инвентарный номер",
    m.НАИМЕНОВАНИЕ as "Наименование материала", 
    m.ЕДИНИЦА_ИЗМЕРЕНИЯ as "Ед. изм.",
    SUM(j.КОЛИЧЕСТВО) as "Количество",
    SUM(j.ОБЩИЕ_ТРУДОЗАТРАТЫ) as "Трудозатраты",
    j.ДАТА_ВЫПУСКА as "Дата"
FROM ЖУРНАЛ_ВЫПУСКА j 
JOIN МАТЕРИАЛЫ m ON j.МАТЕРИАЛ_ID = m.ID 
WHERE j.ДАТА_ВЫПУСКА BETWEEN :ДАТА_НАЧАЛА AND :ДАТА_ОКОНЧАНИЯ
GROUP BY m.ID, m.ИНВЕНТАРНЫЙ_НОМЕР, m.НАИМЕНОВАНИЕ, m.ЕДИНИЦА_ИЗМЕРЕНИЯ, j.ДАТА_ВЫПУСКА
ORDER BY j.ДАТА_ВЫПУСКА DESC, SUM(j.КОЛИЧЕСТВО) DESC
```

2. **Сохраните** под именем `Отчёт_по_периоду`

### **Шаг 3: Создание визуального отчёта**

1. **Перейдите в раздел "Отчёты"**
2. **Создайте новый отчёт** через "Мастер отчётов"
3. **Выберите запрос** `Отчёт_по_материалам_за_месяц`
4. **Добавьте все поля** в отчёт
5. **Настройте группировку** по инвентарному номеру
6. **Добавьте итоговые суммы** для количества и трудозатрат

---

## ⚙️ 3. НАСТРОЙКА МАКРОСОВ

### **Шаг 1: Загрузка макросов**

1. **Откройте LibreOffice Base**
2. **Меню "Сервис"** → "Макросы" → "Управление макросами" → "LibreOffice Basic"
3. **Выберите вашу базу данных** в дереве
4. **Создайте новый модуль** или откройте существующий
5. **Скопируйте код** из файла `macros.bas`

### **Шаг 2: Настройка безопасности макросов**

1. **Меню "Сервис"** → "Параметры"
2. **LibreOffice** → "Безопасность"
3. **Безопасность макросов** → "Средний уровень"
4. **Разрешите выполнение макросов** для вашего документа

---

## 🎯 4. ИСПОЛЬЗОВАНИЕ СИСТЕМЫ

### **Автозаполнение при вводе:**

1. **Выберите материал** из выпадающего списка
   - Автоматически заполнятся: инвентарный номер, трудозатраты
2. **Или введите название** материала в текстовое поле
   - Система найдёт материал и заполнит связанные поля
3. **Введите количество**
   - Автоматически рассчитаются общие трудозатраты

### **Создание отчётов:**

1. **Для отчёта за месяц:**
   - Откройте запрос `Отчёт_по_материалам_за_месяц`
   - Введите год и месяц в параметрах
   - Получите сводку по всем материалам

2. **Для отчёта за период:**
   - Откройте запрос `Отчёт_по_периоду`
   - Укажите начальную и конечную даты
   - Получите детальный отчёт

---

## 🔍 5. ПРИМЕРЫ ИСПОЛЬЗОВАНИЯ

### **Пример 1: Ввод новой записи**
1. Откройте форму ввода
2. Выберите дату: `15.01.2025`
3. Начните вводить название: `Болт М8`
4. Система автоматически найдёт материал и заполнит:
   - Инвентарный номер: `БЛТ-001`
   - Трудозатраты на единицу: `0.5`
5. Введите количество: `100`
6. Система рассчитает общие трудозатраты: `50.0`

### **Пример 2: Создание отчёта за январь 2025**
1. Откройте запрос `Отчёт_по_материалам_за_месяц`
2. Введите параметры:
   - ГОД: `2025`
   - МЕСЯЦ: `1`
3. Получите отчёт с данными:
   - Болт М8x20 (БЛТ-001): 100 шт, 50.0 трудозатрат
   - Гайка М8 (ГК-001): 50 шт, 15.0 трудозатрат

---

## ⚠️ 6. ВАЖНЫЕ ЗАМЕЧАНИЯ

### **Для корректной работы автозаполнения:**
- Убедитесь, что все материалы имеют уникальные названия
- Проверьте, что поля формы имеют правильные имена
- Макросы должны быть разрешены к выполнению

### **Для отчётов:**
- Даты должны быть в правильном формате
- Проверьте наличие данных за выбранный период
- Используйте параметры для фильтрации данных

### **Резервное копирование:**
- Регулярно создавайте резервные копии базы данных
- Сохраняйте копии макросов и запросов
- Тестируйте изменения на копии базы

---

## 🚀 7. ДОПОЛНИТЕЛЬНЫЕ ВОЗМОЖНОСТИ

### **Расширенный поиск материалов:**
- Поиск по частичному совпадению названия
- Поиск по инвентарному номеру
- Автодополнение при вводе

### **Дополнительные отчёты:**
- Отчёт по трудозатратам
- Анализ использования материалов
- Сравнение периодов

### **Автоматизация:**
- Автоматический расчёт себестоимости
- Уведомления о низких остатках
- Экспорт отчётов в Excel

---

**✅ После выполнения всех настроек ваша система будет автоматически заполнять связанные поля и создавать удобные отчёты по расходу материалов!**