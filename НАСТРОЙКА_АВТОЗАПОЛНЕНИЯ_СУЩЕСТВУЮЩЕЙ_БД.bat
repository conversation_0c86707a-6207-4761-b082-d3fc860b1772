@echo off
chcp 65001 >nul
title Настройка автозаполнения для существующей базы данных

echo.
echo ╔═══════════════════════════════════════════════════════════════════════════╗
echo ║                    🔧 НАСТРОЙКА АВТОЗАПОЛНЕНИЯ                           ║
echo ║                   ДЛЯ СУЩЕСТВУЮЩЕЙ БАЗЫ ДАННЫХ                          ║
echo ║            "1.4 Электронный журнал выпуска продукции"                   ║
echo ╚═══════════════════════════════════════════════════════════════════════════╝
echo.

:: Проверка наличия базы данных
set "DB_FILE=1.4 Электронный журнал выпуска продукции.odb"
if not exist "%DB_FILE%" (
    echo ❌ База данных не найдена: %DB_FILE%
    echo 💡 Убедитесь, что файл находится в текущей папке
    pause
    exit /b 1
)

echo ✅ База данных найдена: %DB_FILE%
echo.

:: Поиск LibreOffice
echo 🔍 Поиск LibreOffice...
call :find_libreoffice

if "%LIBREOFFICE_PATH%"=="" (
    echo ❌ LibreOffice не найден!
    echo 💡 Установите LibreOffice с официального сайта
    pause
    exit /b 1
)

echo ✅ LibreOffice найден: %LIBREOFFICE_PATH%
echo.

:: Главное меню
:main_menu
echo ╔═══════════════════════════════════════════════════════════════════════════╗
echo ║                              ГЛАВНОЕ МЕНЮ                                ║
echo ╠═══════════════════════════════════════════════════════════════════════════╣
echo ║                                                                           ║
echo ║  🚀 1. ОТКРЫТЬ БД И НАСТРОИТЬ АВТОЗАПОЛНЕНИЕ                             ║
echo ║     • Открытие существующей БД + пошаговая настройка                     ║
echo ║                                                                           ║
echo ║  ⚙️  2. СОЗДАТЬ ТОЛЬКО МАКРОСЫ                                            ║
echo ║     • Создание файла macros.bas для копирования                          ║
echo ║                                                                           ║
echo ║  📊 3. СОЗДАТЬ ЗАПРОСЫ ДЛЯ ОТЧЕТОВ                                       ║
echo ║     • SQL-запросы для создания отчетов                                   ║
echo ║                                                                           ║
echo ║  📋 4. ПОКАЗАТЬ ИНСТРУКЦИЮ ПО НАСТРОЙКЕ                                  ║
echo ║     • Подробное руководство по настройке форм                            ║
echo ║                                                                           ║
echo ║  🔍 5. АНАЛИЗ СТРУКТУРЫ СУЩЕСТВУЮЩЕЙ БД                                  ║
echo ║     • Помощь в определении таблиц и полей                                ║
echo ║                                                                           ║
echo ║  ❌ 0. ВЫХОД                                                             ║
echo ║                                                                           ║
echo ╚═══════════════════════════════════════════════════════════════════════════╝
echo.
set /p choice="Выберите действие (0-5): "

if "%choice%"=="1" goto :setup_existing_db
if "%choice%"=="2" goto :create_macros_only
if "%choice%"=="3" goto :create_queries_only
if "%choice%"=="4" goto :show_instructions
if "%choice%"=="5" goto :analyze_db_structure
if "%choice%"=="0" goto :exit

echo ❌ Неверный выбор! Попробуйте снова.
goto :main_menu

:: ═══════════════════════════════════════════════════════════════════════════
:: НАСТРОЙКА СУЩЕСТВУЮЩЕЙ БД
:: ═══════════════════════════════════════════════════════════════════════════

:setup_existing_db
echo.
echo ╔═══════════════════════════════════════════════════════════════════════════╗
echo ║                    🚀 НАСТРОЙКА СУЩЕСТВУЮЩЕЙ БД                          ║
echo ╚═══════════════════════════════════════════════════════════════════════════╝
echo.

echo 📋 План настройки:
echo    1️⃣ Открытие существующей базы данных
echo    2️⃣ Анализ структуры таблиц
echo    3️⃣ Создание макросов для автозаполнения
echo    4️⃣ Настройка формы с автозаполнением
echo    5️⃣ Тестирование работы
echo.

echo 🔧 Открытие базы данных...
start "" "%LIBREOFFICE_PATH%soffice.exe" --base "%CD%\%DB_FILE%"

echo.
echo ⏳ Ожидание загрузки LibreOffice Base...
timeout /t 5 /nobreak >nul

echo.
echo ╔═══════════════════════════════════════════════════════════════════════════╗
echo ║                        📋 ПОШАГОВЫЕ ДЕЙСТВИЯ                             ║
echo ╚═══════════════════════════════════════════════════════════════════════════╝
echo.

echo 1️⃣ АНАЛИЗ СТРУКТУРЫ БД:
echo    • В LibreOffice Base перейдите в раздел "Таблицы"
echo    • Найдите таблицы с материалами и журналом выпуска
echo    • Запомните точные названия таблиц и полей
echo.
pause

echo 2️⃣ СОЗДАНИЕ МАКРОСОВ:
echo    • Нажмите Alt+F11 (редактор макросов)
echo    • Создайте модуль "AutoFillModule"
echo    • Скопируйте код из файла macros.bas
echo.
call :create_macros
start notepad "macros.bas"
pause

echo 3️⃣ СОЗДАНИЕ/РЕДАКТИРОВАНИЕ ФОРМЫ:
echo    • Перейдите в раздел "Формы"
echo    • Создайте новую форму или отредактируйте существующую
echo    • Источник данных: ваша таблица журнала выпуска
echo    • Добавьте поля согласно инструкции
echo.
call :show_form_instructions
pause

echo 4️⃣ НАСТРОЙКА СОБЫТИЙ:
echo    • Для каждого поля настройте события:
echo      - Поле выбора материала → OnMaterialChange
echo      - Поле поиска по названию → OnMaterialNameChange
echo      - Поле количества → OnQuantityChange
echo.
pause

echo 5️⃣ ТЕСТИРОВАНИЕ:
echo    • Откройте созданную форму
echo    • Протестируйте автозаполнение
echo    • Проверьте расчет трудозатрат
echo.

goto :success

:: ═══════════════════════════════════════════════════════════════════════════
:: СОЗДАНИЕ МАКРОСОВ
:: ═══════════════════════════════════════════════════════════════════════════

:create_macros_only
echo.
echo ⚙️ Создание макросов для автозаполнения...
call :create_macros
echo ✅ Файл макросов создан: macros.bas
start notepad "macros.bas"
goto :main_menu

:create_macros
if exist "macros.bas" (
    echo ✅ Макросы уже существуют
    goto :eof
)

echo REM Макросы для автозаполнения LibreOffice Base > "macros.bas"
echo REM Электронный журнал выпуска продукции >> "macros.bas"
echo REM Адаптировано для существующей базы данных >> "macros.bas"
echo. >> "macros.bas"
echo REM ВАЖНО: Измените названия таблиц и полей согласно вашей БД! >> "macros.bas"
echo. >> "macros.bas"
echo REM Макрос автозаполнения при выборе материала по ID >> "macros.bas"
echo Sub OnMaterialChange^(oEvent^) >> "macros.bas"
echo     Dim oForm As Object >> "macros.bas"
echo     Dim oMaterialCombo As Object >> "macros.bas"
echo     Dim oConnection As Object >> "macros.bas"
echo     Dim oStatement As Object >> "macros.bas"
echo     Dim oResultSet As Object >> "macros.bas"
echo     Dim sSql As String >> "macros.bas"
echo. >> "macros.bas"
echo     oForm = oEvent.Source.getModel^(^).getParent^(^) >> "macros.bas"
echo     oMaterialCombo = oForm.getByName^("МАТЕРИАЛ_ID"^) >> "macros.bas"
echo. >> "macros.bas"
echo     If oMaterialCombo.getCurrentValue^(^) ^<^> "" Then >> "macros.bas"
echo         oConnection = oForm.getParent^(^).getConnection^(^) >> "macros.bas"
echo         REM ИЗМЕНИТЕ название таблицы на вашу таблицу материалов >> "macros.bas"
echo         sSql = "SELECT ИНВЕНТАРНЫЙ_НОМЕР, ЕДИНИЦА_ИЗМЕРЕНИЯ, ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ FROM ВАША_ТАБЛИЦА_МАТЕРИАЛОВ WHERE ID = " ^& oMaterialCombo.getCurrentValue^(^) >> "macros.bas"
echo         oStatement = oConnection.createStatement^(^) >> "macros.bas"
echo         oResultSet = oStatement.executeQuery^(sSql^) >> "macros.bas"
echo. >> "macros.bas"
echo         If oResultSet.next^(^) Then >> "macros.bas"
echo             SetFieldValue^(oForm, "ИНВЕНТАРНЫЙ_НОМЕР", oResultSet.getString^(1^)^) >> "macros.bas"
echo             SetFieldValue^(oForm, "ЕДИНИЦА_ИЗМЕРЕНИЯ", oResultSet.getString^(2^)^) >> "macros.bas"
echo             SetFieldValue^(oForm, "ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ", oResultSet.getDouble^(3^)^) >> "macros.bas"
echo             CalculateTotalLabor^(oForm^) >> "macros.bas"
echo         End If >> "macros.bas"
echo         oResultSet.close^(^) >> "macros.bas"
echo         oStatement.close^(^) >> "macros.bas"
echo     End If >> "macros.bas"
echo End Sub >> "macros.bas"
echo. >> "macros.bas"
echo REM Макрос автозаполнения при вводе наименования материала >> "macros.bas"
echo Sub OnMaterialNameChange^(oEvent^) >> "macros.bas"
echo     Dim oForm As Object >> "macros.bas"
echo     Dim oNameField As Object >> "macros.bas"
echo     Dim oConnection As Object >> "macros.bas"
echo     Dim oStatement As Object >> "macros.bas"
echo     Dim oResultSet As Object >> "macros.bas"
echo     Dim sSql As String >> "macros.bas"
echo     Dim sName As String >> "macros.bas"
echo. >> "macros.bas"
echo     oForm = oEvent.Source.getModel^(^).getParent^(^) >> "macros.bas"
echo     oNameField = oEvent.Source >> "macros.bas"
echo     sName = Trim^(oNameField.getText^(^)^) >> "macros.bas"
echo. >> "macros.bas"
echo     If Len^(sName^) ^> 2 Then >> "macros.bas"
echo         oConnection = oForm.getParent^(^).getConnection^(^) >> "macros.bas"
echo         REM ИЗМЕНИТЕ название таблицы на вашу таблицу материалов >> "macros.bas"
echo         sSql = "SELECT ID, ИНВЕНТАРНЫЙ_НОМЕР, ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ FROM ВАША_ТАБЛИЦА_МАТЕРИАЛОВ WHERE UPPER^(НАИМЕНОВАНИЕ^) LIKE UPPER^('%" ^& sName ^& "%'^) ORDER BY НАИМЕНОВАНИЕ LIMIT 1" >> "macros.bas"
echo         oStatement = oConnection.createStatement^(^) >> "macros.bas"
echo         oResultSet = oStatement.executeQuery^(sSql^) >> "macros.bas"
echo. >> "macros.bas"
echo         If oResultSet.next^(^) Then >> "macros.bas"
echo             SetFieldValue^(oForm, "МАТЕРИАЛ_ID", oResultSet.getLong^(1^)^) >> "macros.bas"
echo             SetFieldValue^(oForm, "ИНВЕНТАРНЫЙ_НОМЕР", oResultSet.getString^(2^)^) >> "macros.bas"
echo             SetFieldValue^(oForm, "ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ", oResultSet.getDouble^(3^)^) >> "macros.bas"
echo             CalculateTotalLabor^(oForm^) >> "macros.bas"
echo         End If >> "macros.bas"
echo         oResultSet.close^(^) >> "macros.bas"
echo         oStatement.close^(^) >> "macros.bas"
echo     End If >> "macros.bas"
echo End Sub >> "macros.bas"
echo. >> "macros.bas"
echo REM Пересчет трудозатрат при изменении количества >> "macros.bas"
echo Sub OnQuantityChange^(oEvent^) >> "macros.bas"
echo     Dim oForm As Object >> "macros.bas"
echo     oForm = oEvent.Source.getModel^(^).getParent^(^) >> "macros.bas"
echo     CalculateTotalLabor^(oForm^) >> "macros.bas"
echo End Sub >> "macros.bas"
echo. >> "macros.bas"
echo REM Пересчет трудозатрат >> "macros.bas"
echo Sub CalculateTotalLabor^(oForm As Object^) >> "macros.bas"
echo     Dim dQuantity As Double >> "macros.bas"
echo     Dim dLaborPerUnit As Double >> "macros.bas"
echo. >> "macros.bas"
echo     On Error Resume Next >> "macros.bas"
echo     dQuantity = GetFieldValue^(oForm, "КОЛИЧЕСТВО"^) >> "macros.bas"
echo     dLaborPerUnit = GetFieldValue^(oForm, "ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ"^) >> "macros.bas"
echo. >> "macros.bas"
echo     If dQuantity ^> 0 And dLaborPerUnit ^> 0 Then >> "macros.bas"
echo         SetFieldValue^(oForm, "ОБЩИЕ_ТРУДОЗАТРАТЫ", dQuantity * dLaborPerUnit^) >> "macros.bas"
echo     End If >> "macros.bas"
echo End Sub >> "macros.bas"
echo. >> "macros.bas"
echo REM Вспомогательные функции >> "macros.bas"
echo Function GetFieldValue^(oForm As Object, sFieldName As String^) As Variant >> "macros.bas"
echo     Dim oField As Object >> "macros.bas"
echo     On Error Resume Next >> "macros.bas"
echo     oField = oForm.getByName^(sFieldName^) >> "macros.bas"
echo     If Not IsNull^(oField^) Then >> "macros.bas"
echo         If oField.supportsService^("com.sun.star.form.component.NumericField"^) Then >> "macros.bas"
echo             GetFieldValue = oField.getValue^(^) >> "macros.bas"
echo         Else >> "macros.bas"
echo             GetFieldValue = oField.getText^(^) >> "macros.bas"
echo         End If >> "macros.bas"
echo     Else >> "macros.bas"
echo         GetFieldValue = 0 >> "macros.bas"
echo     End If >> "macros.bas"
echo End Function >> "macros.bas"
echo. >> "macros.bas"
echo Sub SetFieldValue^(oForm As Object, sFieldName As String, vValue As Variant^) >> "macros.bas"
echo     Dim oField As Object >> "macros.bas"
echo     On Error Resume Next >> "macros.bas"
echo     oField = oForm.getByName^(sFieldName^) >> "macros.bas"
echo     If Not IsNull^(oField^) Then >> "macros.bas"
echo         If oField.supportsService^("com.sun.star.form.component.NumericField"^) Then >> "macros.bas"
echo             oField.setValue^(vValue^) >> "macros.bas"
echo         Else >> "macros.bas"
echo             oField.setText^(CStr^(vValue^)^) >> "macros.bas"
echo         End If >> "macros.bas"
echo     End If >> "macros.bas"
echo End Sub >> "macros.bas"

goto :eof

:: ═══════════════════════════════════════════════════════════════════════════
:: ДРУГИЕ ФУНКЦИИ
:: ═══════════════════════════════════════════════════════════════════════════

:create_queries_only
echo.
echo 📊 Создание запросов для отчетов...
call :create_queries
echo ✅ Файл запросов создан: queries_for_existing_db.sql
start notepad "queries_for_existing_db.sql"
goto :main_menu

:create_queries
echo -- Запросы для отчетов - адаптированы для существующей БД > "queries_for_existing_db.sql"
echo -- ВАЖНО: Измените названия таблиц и полей согласно вашей БД! >> "queries_for_existing_db.sql"
echo. >> "queries_for_existing_db.sql"
echo -- 1. Поиск материала по наименованию >> "queries_for_existing_db.sql"
echo SELECT ID, ИНВЕНТАРНЫЙ_НОМЕР, ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ >> "queries_for_existing_db.sql"
echo FROM ВАША_ТАБЛИЦА_МАТЕРИАЛОВ >> "queries_for_existing_db.sql"
echo WHERE UPPER^(НАИМЕНОВАНИЕ^) LIKE UPPER^(:НАИМЕНОВАНИЕ_ПОИСК^); >> "queries_for_existing_db.sql"
echo. >> "queries_for_existing_db.sql"
echo -- 2. Журнал с автозаполнением >> "queries_for_existing_db.sql"
echo SELECT j.ID, j.ДАТА_ВЫПУСКА, j.МАТЕРИАЛ_ID, >> "queries_for_existing_db.sql"
echo        m.НАИМЕНОВАНИЕ as НАЗВАНИЕ_МАТЕРИАЛА, >> "queries_for_existing_db.sql"
echo        m.ИНВЕНТАРНЫЙ_НОМЕР, m.ЕДИНИЦА_ИЗМЕРЕНИЯ, >> "queries_for_existing_db.sql"
echo        j.КОЛИЧЕСТВО, m.ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ, >> "queries_for_existing_db.sql"
echo        j.ОБЩИЕ_ТРУДОЗАТРАТЫ, j.ПРИМЕЧАНИЯ >> "queries_for_existing_db.sql"
echo FROM ВАША_ТАБЛИЦА_ЖУРНАЛА j >> "queries_for_existing_db.sql"
echo LEFT JOIN ВАША_ТАБЛИЦА_МАТЕРИАЛОВ m ON j.МАТЕРИАЛ_ID = m.ID >> "queries_for_existing_db.sql"
echo ORDER BY j.ДАТА_ВЫПУСКА DESC, j.ID DESC; >> "queries_for_existing_db.sql"
goto :eof

:show_instructions
echo.
echo 📋 Открытие инструкций...
call :create_existing_db_instructions
start notepad "ИНСТРУКЦИЯ_ДЛЯ_СУЩЕСТВУЮЩЕЙ_БД.md"
goto :main_menu

:create_existing_db_instructions
echo # 🔧 НАСТРОЙКА АВТОЗАПОЛНЕНИЯ ДЛЯ СУЩЕСТВУЮЩЕЙ БД > "ИНСТРУКЦИЯ_ДЛЯ_СУЩЕСТВУЮЩЕЙ_БД.md"
echo ## Пошаговое руководство >> "ИНСТРУКЦИЯ_ДЛЯ_СУЩЕСТВУЮЩЕЙ_БД.md"
echo. >> "ИНСТРУКЦИЯ_ДЛЯ_СУЩЕСТВУЮЩЕЙ_БД.md"
echo ### ШАГ 1: Анализ структуры БД >> "ИНСТРУКЦИЯ_ДЛЯ_СУЩЕСТВУЮЩЕЙ_БД.md"
echo 1. Откройте вашу БД в LibreOffice Base >> "ИНСТРУКЦИЯ_ДЛЯ_СУЩЕСТВУЮЩЕЙ_БД.md"
echo 2. Перейдите в раздел "Таблицы" >> "ИНСТРУКЦИЯ_ДЛЯ_СУЩЕСТВУЮЩЕЙ_БД.md"
echo 3. Найдите таблицы с материалами и журналом >> "ИНСТРУКЦИЯ_ДЛЯ_СУЩЕСТВУЮЩЕЙ_БД.md"
echo 4. Запишите точные названия таблиц и полей >> "ИНСТРУКЦИЯ_ДЛЯ_СУЩЕСТВУЮЩЕЙ_БД.md"
echo. >> "ИНСТРУКЦИЯ_ДЛЯ_СУЩЕСТВУЮЩЕЙ_БД.md"
echo ### ШАГ 2: Адаптация макросов >> "ИНСТРУКЦИЯ_ДЛЯ_СУЩЕСТВУЮЩЕЙ_БД.md"
echo 1. Откройте файл macros.bas >> "ИНСТРУКЦИЯ_ДЛЯ_СУЩЕСТВУЮЩЕЙ_БД.md"
echo 2. Замените "ВАША_ТАБЛИЦА_МАТЕРИАЛОВ" на реальное название >> "ИНСТРУКЦИЯ_ДЛЯ_СУЩЕСТВУЮЩЕЙ_БД.md"
echo 3. Замените "ВАША_ТАБЛИЦА_ЖУРНАЛА" на реальное название >> "ИНСТРУКЦИЯ_ДЛЯ_СУЩЕСТВУЮЩЕЙ_БД.md"
echo 4. Проверьте названия полей >> "ИНСТРУКЦИЯ_ДЛЯ_СУЩЕСТВУЮЩЕЙ_БД.md"
goto :eof

:show_form_instructions
echo.
echo 📋 ИНСТРУКЦИЯ ПО НАСТРОЙКЕ ФОРМЫ:
echo.
echo 🔹 Поля формы должны иметь следующие имена:
echo    • МАТЕРИАЛ_ID - выпадающий список материалов
echo    • НАИМЕНОВАНИЕ_ПОИСК - поле для поиска по названию
echo    • ИНВЕНТАРНЫЙ_НОМЕР - поле автозаполнения (только чтение)
echo    • КОЛИЧЕСТВО - поле ввода количества
echo    • ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ - поле автозаполнения (только чтение)
echo    • ОБЩИЕ_ТРУДОЗАТРАТЫ - поле автозаполнения (только чтение)
echo.
echo 🔹 События полей:
echo    • МАТЕРИАЛ_ID → При изменении → AutoFillModule.OnMaterialChange
echo    • НАИМЕНОВАНИЕ_ПОИСК → При изменении → AutoFillModule.OnMaterialNameChange
echo    • КОЛИЧЕСТВО → При изменении → AutoFillModule.OnQuantityChange
echo.
goto :eof

:analyze_db_structure
echo.
echo 🔍 АНАЛИЗ СТРУКТУРЫ СУЩЕСТВУЮЩЕЙ БД
echo.
echo 📋 Для настройки автозаполнения нужно определить:
echo.
echo 1️⃣ ТАБЛИЦА МАТЕРИАЛОВ:
echo    • Название таблицы: _______________
echo    • Поле ID: _______________
echo    • Поле наименования: _______________
echo    • Поле инвентарного номера: _______________
echo    • Поле трудозатрат: _______________
echo.
echo 2️⃣ ТАБЛИЦА ЖУРНАЛА ВЫПУСКА:
echo    • Название таблицы: _______________
echo    • Поле ID материала: _______________
echo    • Поле количества: _______________
echo    • Поле общих трудозатрат: _______________
echo.
echo 💡 Откройте вашу БД и заполните эту информацию
echo    Затем адаптируйте макросы под ваши названия
echo.
pause
goto :main_menu

:find_libreoffice
set "LIBREOFFICE_PATH="
for %%d in (C D E F G H) do (
    if exist "%%d:\Program Files\LibreOffice\program\soffice.exe" (
        set "LIBREOFFICE_PATH=%%d:\Program Files\LibreOffice\program\"
        goto :found_libreoffice
    )
    if exist "%%d:\Program Files (x86)\LibreOffice\program\soffice.exe" (
        set "LIBREOFFICE_PATH=%%d:\Program Files (x86)\LibreOffice\program\"
        goto :found_libreoffice
    )
)
:found_libreoffice
goto :eof

:success
echo.
echo ╔═══════════════════════════════════════════════════════════════════════════╗
echo ║                    🎉 НАСТРОЙКА ЗАВЕРШЕНА!                               ║
echo ╚═══════════════════════════════════════════════════════════════════════════╝
echo.
echo ✅ Автозаполнение настроено для вашей существующей БД!
echo.
echo 💡 Не забудьте:
echo    • Адаптировать названия таблиц в макросах
echo    • Проверить названия полей
echo    • Протестировать работу автозаполнения
echo.
pause
goto :main_menu

:exit
echo.
echo 👋 До свидания!
exit /b 0
