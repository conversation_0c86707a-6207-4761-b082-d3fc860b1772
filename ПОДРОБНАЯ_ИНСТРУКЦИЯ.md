# 📋 ПОДРОБНАЯ ИНСТРУКЦИЯ ПО ИСПОЛЬЗОВАНИЮ
## Автоматизированное создание базы данных LibreOffice

---

## 🚀 1. ПОДГОТОВКА ФАЙЛОВ

### Структура папки:
```
📁 1.4 Электронный журнал выпуска продукции\
├── 📄 setup_database.bat          # Главный пакетный файл
├── 📄 create_tables.sql           # SQL структура БД
├── 📄 queries.sql                 # Готовые запросы
├── 📄 macros.bas                  # Макросы LibreOffice
├── 📄 LibreOffice_Setup_Guide.md  # Краткое руководство
├── 📄 ПОДРОБНАЯ_ИНСТРУКЦИЯ.md     # Этот файл
└── 🗃️ [Здесь будет создана БД].odb
```

---

## 🎯 2. СПОСОБЫ ЗАПУСКА

### **Способ 1 (Простой):**
1. **Дважды щелкните** по файлу `setup_database.bat`
2. Появится окно командной строки с меню

### **Способ 2 (Через командную строку):**
1. Нажмите **Win+R** → введите `cmd` → Enter
2. Перейдите в папку: `cd "путь_к_вашей_папке"`
3. Выполните: `setup_database.bat`

### **Способ 3 (Из проводника):**
1. Откройте папку с файлами
2. Щелкните правой кнопкой на `setup_database.bat`
3. Выберите "Запуск от имени администратора" (при необходимости)

---

## 🖥️ 3. ИНТЕРФЕЙС И ВЫБОР МЕТОДА

После запуска вы увидите меню:
```
========================================
  АВТОМАТИЧЕСКОЕ СОЗДАНИЕ БД LIBREOFFICE
========================================

✓ LibreOffice найден: D:\Office\program\

📁 Путь к БД: [путь_к_папке]\Журнал_выпуска.odb

Выберите действие:
1. Создать все файлы (SQL + макросы + руководство)
2. Создать только SQL структуру
3. Создать только макросы
4. Создать только руководство
5. Полная настройка (открыть LibreOffice)

Введите номер (1-5):
```

---

## 📋 4. ОПИСАНИЕ МЕТОДОВ

### **Метод 1 - Создать все файлы (Рекомендуется)**
- ✅ Создает полную SQL структуру
- ✅ Генерирует готовые запросы
- ✅ Создает макросы для автоматизации
- ✅ Формирует подробное руководство
- ⚡ **Результат:** Готовый набор для создания БД

### **Метод 2 - Только SQL структура**
- ✅ Создает файл `create_tables.sql`
- ✅ Создает файл `queries.sql`
- 📝 Подходит для ручного импорта
- 🔧 Полезно для понимания структуры

### **Метод 3 - Только макросы**
- ✅ Создает файл `macros.bas`
- 🔧 Для автоматизации форм
- 📊 Упрощает ввод данных

### **Метод 4 - Только руководство**
- ✅ Создает `LibreOffice_Setup_Guide.md`
- 📖 Пошаговые инструкции
- 🎓 Подходит для обучения

### **Метод 5 - Полная настройка**
- 🚀 Автоматически запускает LibreOffice Base
- 📖 Открывает руководство
- 🔧 Для ручной работы с БД

---

## ⚙️ 5. СИСТЕМНЫЕ ТРЕБОВАНИЯ

### **Обязательно:**
- 🖥️ Windows 7/8/10/11
- 📊 LibreOffice (любая современная версия)
- 💾 50 МБ свободного места

### **Рекомендуется:**
- 🔧 Права администратора
- 🌐 Подключение к интернету (для обновлений)

---

## 🔧 6. ВОЗМОЖНЫЕ ПРОБЛЕМЫ И РЕШЕНИЯ

### **Проблема:** "LibreOffice не найден"
**Решения:**
1. Установите LibreOffice с официального сайта
2. Проверьте путь установки
3. Перезапустите скрипт

### **Проблема:** "Ошибка доступа к файлам"
**Решения:**
1. Запустите от имени администратора
2. Проверьте права на папку
3. Закройте LibreOffice перед запуском

### **Проблема:** "Файл уже существует"
**Решения:**
1. Скрипт предложит перезаписать
2. Сделайте резервную копию
3. Выберите другое имя файла

### **Проблема:** Скрипт не запускается
**Решения:**
1. Проверьте, что файл не заблокирован
2. Временно отключите антивирус
3. Запустите через командную строку

---

## 🎯 7. ЧТО ПОЛУЧИТСЯ В РЕЗУЛЬТАТЕ

После успешного выполнения у вас будет:

### **Файлы базы данных:**
- 🗃️ `[Имя].odb` - готовая база данных LibreOffice
- 📄 `create_tables.sql` - SQL команды для создания структуры
- 📄 `queries.sql` - готовые запросы для отчетов

### **Структура базы данных:**
- 📊 **Таблица МАТЕРИАЛЫ** - справочник материалов
- 📊 **Таблица СОТРУДНИКИ** - данные сотрудников
- 📊 **Таблица ЖУРНАЛ_ВЫПУСКА** - основная таблица записей
- 🔗 **Связи между таблицами** - для целостности данных
- 🔍 **Индексы** - для быстрого поиска

### **Готовые запросы:**
- 📈 Отчет по выпуску за период
- 👤 Отчет по сотрудникам
- 📦 Отчет по материалам
- 📊 Сводная статистика

### **Макросы автоматизации:**
- ⚡ Автозаполнение полей
- 🔄 Обновление данных
- ✅ Проверка корректности ввода

---

## 🚀 8. ДАЛЬНЕЙШИЕ ДЕЙСТВИЯ

### **Шаг 1: Откройте созданную БД**
1. Дважды щелкните по файлу `.odb`
2. LibreOffice Base откроется автоматически
3. Изучите созданную структуру

### **Шаг 2: Проверьте таблицы**
1. Перейдите в раздел "Таблицы"
2. Откройте каждую таблицу
3. Проверьте структуру и связи

### **Шаг 3: Создайте формы**
1. Используйте "Мастер форм"
2. Выберите таблицу ЖУРНАЛ_ВЫПУСКА
3. Добавьте поля из связанных таблиц

### **Шаг 4: Настройте отчеты**
1. Используйте готовые запросы
2. Создайте отчеты через "Мастер отчетов"
3. Настройте форматирование

### **Шаг 5: Добавьте реальные данные**
1. Замените тестовые данные
2. Заполните справочники
3. Начните ведение журнала

---

## 🔧 9. ДОПОЛНИТЕЛЬНЫЕ ВОЗМОЖНОСТИ

### **Изменение параметров:**
Отредактируйте начало файла `setup_database.bat`:
```batch
set "DB_NAME=МояБазаДанных"          # Имя БД
set "DB_PATH=%~dp0%DB_NAME%.odb"     # Путь к БД
```

### **Добавление своих таблиц:**
1. Отредактируйте файл `create_tables.sql`
2. Добавьте новые CREATE TABLE команды
3. Обновите связи и индексы

### **Настройка макросов:**
1. Откройте файл `macros.bas`
2. Измените функции под свои нужды
3. Добавьте новые процедуры

### **Создание резервных копий:**
```batch
copy "*.odb" "backup_folder\"
copy "*.sql" "backup_folder\"
```

---

## 📞 10. ПОДДЕРЖКА И ПОМОЩЬ

### **Если что-то не работает:**
1. 📖 Перечитайте инструкцию
2. 🔍 Проверьте системные требования
3. 🔧 Попробуйте запустить от администратора
4. 📝 Проверьте логи ошибок

### **Полезные ресурсы:**
- 📚 Документация LibreOffice Base
- 🎓 Руководство по SQL
- 💬 Форум LibreOffice

---

## ✅ 11. КОНТРОЛЬНЫЙ СПИСОК

Перед началом работы убедитесь:
- [ ] LibreOffice установлен
- [ ] Все файлы в одной папке
- [ ] Есть права на запись в папку
- [ ] Антивирус не блокирует файлы
- [ ] Достаточно места на диске

После создания БД проверьте:
- [ ] База данных открывается
- [ ] Таблицы созданы корректно
- [ ] Связи между таблицами работают
- [ ] Запросы выполняются
- [ ] Макросы загружены

---

**🎉 Поздравляем! Ваша система электронного журнала готова к использованию!**

---
*Создано автоматически скриптом setup_database.bat*