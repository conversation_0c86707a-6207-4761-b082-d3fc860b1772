# 📝 СОЗДАНИЕ ФОРМ С АВТОЗАПОЛНЕНИЕМ
## Пошаговое руководство по созданию удобных форм ввода

---

## 🎯 1. СОЗДАНИЕ ОСНОВНОЙ ФОРМЫ ВВОДА

### **Шаг 1: Запуск мастера форм**
1. Откройте LibreOffice Base с вашей базой данных
2. В левой панели выберите **"Формы"**
3. Нажмите **"Создать форму в режиме конструктора"**
4. Или используйте **"Мастер форм"** для быстрого создания

### **Шаг 2: Настройка источника данных**
1. **Основная таблица:** `ЖУРНАЛ_ВЫПУСКА`
2. **Связанные таблицы:** `МАТЕРИАЛЫ` (через МАТЕРИАЛ_ID)
3. **Тип формы:** Форма с подчинённой формой (если нужно)

### **Шаг 3: Добавление полей на форму**

#### **Обязательные поля:**
- **ДАТА_ВЫПУСКА** (DateField)
- **МАТЕРИАЛ_ID** (ComboBox) 
- **НАИМЕНОВАНИЕ_ПОИСК** (TextField) - для поиска материала
- **ИНВЕНТАРНЫЙ_НОМЕР** (TextField, ReadOnly)
- **КОЛИЧЕСТВО** (NumericField)
- **ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ** (NumericField, ReadOnly)
- **ОБЩИЕ_ТРУДОЗАТРАТЫ** (NumericField, ReadOnly)
- **ПРИМЕЧАНИЯ** (TextField)

---

## ⚙️ 2. НАСТРОЙКА АВТОЗАПОЛНЕНИЯ

### **Настройка ComboBox для материалов:**

1. **Выберите поле МАТЕРИАЛ_ID**
2. **Откройте свойства** (F4 или правый клик)
3. **Вкладка "Данные":**
   ```
   Тип содержимого: Список
   Источник списка: SELECT ID, НАИМЕНОВАНИЕ, ИНВЕНТАРНЫЙ_НОМЕР FROM МАТЕРИАЛЫ ORDER BY НАИМЕНОВАНИЕ
   Связанное поле: 0
   Отображаемое поле: 1
   ```

### **Настройка событий для автозаполнения:**

#### **Для поля МАТЕРИАЛ_ID:**
1. **Вкладка "События"**
2. **Событие "При изменении":** `OnMaterialChange`
3. **Язык макроса:** LibreOffice Basic

#### **Для поля НАИМЕНОВАНИЕ_ПОИСК:**
1. **Событие "При изменении":** `OnMaterialNameChange`
2. **Событие "При потере фокуса":** `OnMaterialNameChange`

#### **Для поля КОЛИЧЕСТВО:**
1. **Событие "При изменении":** `OnQuantityChange`

---

## 🔧 3. РАСШИРЕННЫЕ НАСТРОЙКИ ПОЛЕЙ

### **Поле ДАТА_ВЫПУСКА:**
```
Тип: Date Field
Формат даты: ДД.ММ.ГГГГ
Значение по умолчанию: =TODAY()
Обязательное: Да
```

### **Поле МАТЕРИАЛ_ID (ComboBox):**
```
Тип: Combo Box
Выпадающий список: Да
Автодополнение: Да
Источник списка: SELECT ID, CONCAT(НАИМЕНОВАНИЕ, ' (', ИНВЕНТАРНЫЙ_НОМЕР, ')') FROM МАТЕРИАЛЫ ORDER BY НАИМЕНОВАНИЕ
```

### **Поле НАИМЕНОВАНИЕ_ПОИСК:**
```
Тип: Text Field
Максимальная длина: 200
Подсказка: "Введите название материала для поиска"
Автодополнение: Да
```

### **Поле ИНВЕНТАРНЫЙ_НОМЕР:**
```
Тип: Text Field
Только для чтения: Да
Цвет фона: Светло-серый
Максимальная длина: 50
```

### **Поле КОЛИЧЕСТВО:**
```
Тип: Numeric Field
Десятичные знаки: 2
Минимальное значение: 0.01
Максимальное значение: 999999.99
Обязательное: Да
```

### **Поля трудозатрат:**
```
Тип: Numeric Field
Десятичные знаки: 2
Только для чтения: Да
Цвет фона: Светло-желтый
```

---

## 📋 4. СОЗДАНИЕ ФОРМЫ ПОИСКА И ФИЛЬТРАЦИИ

### **Форма быстрого поиска:**

1. **Создайте отдельную форму** для поиска записей
2. **Добавьте поля фильтрации:**
   - Период (дата с/по)
   - Материал (выпадающий список)
   - Инвентарный номер

### **SQL для формы поиска:**
```sql
SELECT j.*, m.НАИМЕНОВАНИЕ, m.ИНВЕНТАРНЫЙ_НОМЕР 
FROM ЖУРНАЛ_ВЫПУСКА j 
LEFT JOIN МАТЕРИАЛЫ m ON j.МАТЕРИАЛ_ID = m.ID 
WHERE (:ДАТА_ОТ IS NULL OR j.ДАТА_ВЫПУСКА >= :ДАТА_ОТ)
  AND (:ДАТА_ДО IS NULL OR j.ДАТА_ВЫПУСКА <= :ДАТА_ДО)
  AND (:МАТЕРИАЛ_ID IS NULL OR j.МАТЕРИАЛ_ID = :МАТЕРИАЛ_ID)
ORDER BY j.ДАТА_ВЫПУСКА DESC
```

---

## 🎨 5. УЛУЧШЕНИЕ ИНТЕРФЕЙСА

### **Группировка полей:**
1. **Группа "Основные данные":**
   - Дата выпуска
   - Материал (выбор и поиск)

2. **Группа "Автозаполняемые данные":**
   - Инвентарный номер
   - Трудозатраты на единицу

3. **Группа "Расчётные данные":**
   - Количество
   - Общие трудозатраты

### **Цветовое кодирование:**
- **Обязательные поля:** Белый фон
- **Автозаполняемые:** Светло-серый фон  
- **Расчётные:** Светло-жёлтый фон
- **Примечания:** Светло-голубой фон

### **Подсказки для пользователя:**
```
МАТЕРИАЛ_ID: "Выберите материал из списка"
НАИМЕНОВАНИЕ_ПОИСК: "Или введите название для поиска"
КОЛИЧЕСТВО: "Введите количество (обязательно)"
ИНВЕНТАРНЫЙ_НОМЕР: "Заполняется автоматически"
ОБЩИЕ_ТРУДОЗАТРАТЫ: "Рассчитывается автоматически"
```

---

## 🔄 6. ТЕСТИРОВАНИЕ АВТОЗАПОЛНЕНИЯ

### **Тест 1: Выбор из списка**
1. Откройте форму
2. Выберите материал из выпадающего списка
3. **Ожидаемый результат:**
   - Инвентарный номер заполнился
   - Трудозатраты на единицу заполнились
4. Введите количество
5. **Ожидаемый результат:**
   - Общие трудозатраты рассчитались

### **Тест 2: Поиск по названию**
1. Введите в поле поиска: "Болт"
2. **Ожидаемый результат:**
   - Найден материал "Болт М8x20"
   - Все связанные поля заполнились
   - МАТЕРИАЛ_ID установился на соответствующий ID

### **Тест 3: Изменение количества**
1. Измените количество в уже заполненной записи
2. **Ожидаемый результат:**
   - Общие трудозатраты пересчитались автоматически

---

## ⚠️ 7. УСТРАНЕНИЕ ПРОБЛЕМ

### **Проблема: Макросы не выполняются**
**Решение:**
1. Проверьте настройки безопасности макросов
2. Убедитесь, что макросы сохранены в документе
3. Проверьте правильность имён функций в событиях

### **Проблема: Автозаполнение не работает**
**Решение:**
1. Проверьте имена полей в форме
2. Убедитесь, что поля имеют правильные типы данных
3. Проверьте SQL-запросы в источниках списков

### **Проблема: Неправильные расчёты**
**Решение:**
1. Проверьте типы данных полей (должны быть Numeric)
2. Убедитесь, что поля не содержат NULL значений
3. Проверьте логику расчёта в макросе `CalculateTotalLabor`

---

## 📊 8. СОЗДАНИЕ ФОРМЫ ОТЧЁТОВ

### **Форма выбора параметров отчёта:**

1. **Создайте форму** без привязки к таблице
2. **Добавьте элементы управления:**
   - **Год:** NumericField (2020-2030)
   - **Месяц:** ComboBox (1-12 с названиями)
   - **Дата от:** DateField
   - **Дата до:** DateField
   - **Материал:** ComboBox (опционально)

3. **Добавьте кнопки:**
   - "Создать отчёт за месяц"
   - "Создать отчёт за период"
   - "Экспорт в Excel"

### **Макросы для кнопок отчётов:**
```basic
Sub CreateMonthlyReport()
    ' Код для создания месячного отчёта
    ' Использует значения из полей формы
End Sub

Sub CreatePeriodReport()
    ' Код для создания отчёта за период
End Sub

Sub ExportToExcel()
    ' Код для экспорта данных
End Sub
```

---

## ✅ 9. ФИНАЛЬНАЯ ПРОВЕРКА

### **Контрольный список:**
- [ ] Форма открывается без ошибок
- [ ] Выпадающий список материалов работает
- [ ] Автозаполнение по выбору материала работает
- [ ] Поиск по названию материала работает
- [ ] Автоматический расчёт трудозатрат работает
- [ ] Все поля сохраняются в базу данных
- [ ] Макросы выполняются без ошибок
- [ ] Интерфейс удобен для пользователя

### **Тестовые данные для проверки:**
```
Дата: 15.01.2025
Материал: Болт М8x20
Количество: 50
Ожидаемый результат: 
- Инвентарный номер: БЛТ-001
- Трудозатраты: 25.0
```

---

**🎉 После выполнения всех настроек у вас будет удобная форма с полным автозаполнением связанных данных!**