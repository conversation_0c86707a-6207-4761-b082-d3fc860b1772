#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Автоматическая настройка форм LibreOffice Base
Электронный журнал выпуска продукции

Этот скрипт создает XML-шаблоны форм для LibreOffice Base
с настроенным автозаполнением полей.
"""

import os
import xml.etree.ElementTree as ET
from datetime import datetime

def create_form_template():
    """Создает XML-шаблон формы с автозаполнением"""
    
    # Корневой элемент формы
    form_root = ET.Element("office:document-content")
    form_root.set("xmlns:office", "urn:oasis:names:tc:opendocument:xmlns:office:1.0")
    form_root.set("xmlns:form", "urn:oasis:names:tc:opendocument:xmlns:form:1.0")
    form_root.set("xmlns:script", "urn:oasis:names:tc:opendocument:xmlns:script:1.0")
    
    # Автоматические стили
    automatic_styles = ET.SubElement(form_root, "office:automatic-styles")
    
    # Основное содержимое
    body = ET.SubElement(form_root, "office:body")
    text = ET.SubElement(body, "office:text")
    
    # Форма
    form = ET.SubElement(text, "form:form")
    form.set("form:name", "Журнал_выпуска_форма")
    form.set("form:datasource", ".")
    form.set("form:command-type", "table")
    form.set("form:command", "ЖУРНАЛ_ВЫПУСКА")
    form.set("form:control-implementation", "ooo:com.sun.star.form.component.Form")
    
    # Поле даты выпуска
    date_field = ET.SubElement(form, "form:date")
    date_field.set("form:name", "ДАТА_ВЫПУСКА")
    date_field.set("form:data-field", "ДАТА_ВЫПУСКА")
    date_field.set("form:control-implementation", "ooo:com.sun.star.form.component.DateField")
    
    # Комбо-бокс для выбора материала
    material_combo = ET.SubElement(form, "form:combobox")
    material_combo.set("form:name", "МАТЕРИАЛ_ID")
    material_combo.set("form:data-field", "МАТЕРИАЛ_ID")
    material_combo.set("form:list-source", "SELECT ID, НАИМЕНОВАНИЕ FROM МАТЕРИАЛЫ ORDER BY НАИМЕНОВАНИЕ")
    material_combo.set("form:list-source-type", "sql")
    material_combo.set("form:bound-column", "1")
    material_combo.set("form:control-implementation", "ooo:com.sun.star.form.component.ComboBox")
    
    # Событие изменения материала
    material_event = ET.SubElement(material_combo, "script:event")
    material_event.set("script:event-name", "form:changed")
    material_event.set("script:macro-name", "AutoFillModule.OnMaterialChange")
    material_event.set("script:language", "StarBasic")
    
    # Поле поиска по названию
    search_field = ET.SubElement(form, "form:text")
    search_field.set("form:name", "НАИМЕНОВАНИЕ_ПОИСК")
    search_field.set("form:control-implementation", "ooo:com.sun.star.form.component.TextField")
    
    # Событие изменения названия
    search_event = ET.SubElement(search_field, "script:event")
    search_event.set("script:event-name", "form:changed")
    search_event.set("script:macro-name", "AutoFillModule.OnMaterialNameChange")
    search_event.set("script:language", "StarBasic")
    
    # Поле инвентарного номера (только чтение)
    inventory_field = ET.SubElement(form, "form:text")
    inventory_field.set("form:name", "ИНВЕНТАРНЫЙ_НОМЕР")
    inventory_field.set("form:readonly", "true")
    inventory_field.set("form:control-implementation", "ooo:com.sun.star.form.component.TextField")
    
    # Поле количества
    quantity_field = ET.SubElement(form, "form:formatted-text")
    quantity_field.set("form:name", "КОЛИЧЕСТВО")
    quantity_field.set("form:data-field", "КОЛИЧЕСТВО")
    quantity_field.set("form:control-implementation", "ooo:com.sun.star.form.component.FormattedField")
    
    # Событие изменения количества
    quantity_event = ET.SubElement(quantity_field, "script:event")
    quantity_event.set("script:event-name", "form:changed")
    quantity_event.set("script:macro-name", "AutoFillModule.OnQuantityChange")
    quantity_event.set("script:language", "StarBasic")
    
    # Поле трудозатрат на единицу (только чтение)
    labor_unit_field = ET.SubElement(form, "form:formatted-text")
    labor_unit_field.set("form:name", "ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ")
    labor_unit_field.set("form:readonly", "true")
    labor_unit_field.set("form:control-implementation", "ooo:com.sun.star.form.component.FormattedField")
    
    # Поле общих трудозатрат (только чтение)
    total_labor_field = ET.SubElement(form, "form:formatted-text")
    total_labor_field.set("form:name", "ОБЩИЕ_ТРУДОЗАТРАТЫ")
    total_labor_field.set("form:data-field", "ОБЩИЕ_ТРУДОЗАТРАТЫ")
    total_labor_field.set("form:readonly", "true")
    total_labor_field.set("form:control-implementation", "ooo:com.sun.star.form.component.FormattedField")
    
    # Поле примечаний
    notes_field = ET.SubElement(form, "form:textarea")
    notes_field.set("form:name", "ПРИМЕЧАНИЯ")
    notes_field.set("form:data-field", "ПРИМЕЧАНИЯ")
    notes_field.set("form:control-implementation", "ooo:com.sun.star.form.component.TextArea")
    
    return form_root

def create_setup_instructions():
    """Создает подробные инструкции по настройке"""
    
    instructions = """
# 🔧 АВТОМАТИЧЕСКАЯ НАСТРОЙКА ФОРМ LIBREOFFICE BASE

## 📋 ИНСТРУКЦИЯ ПО ИМПОРТУ ФОРМЫ:

### Шаг 1: Подготовка
1. Убедитесь, что база данных создана и таблицы импортированы
2. Убедитесь, что макросы загружены в LibreOffice Basic

### Шаг 2: Создание формы вручную (рекомендуется)
Поскольку LibreOffice Base не поддерживает прямой импорт XML-форм,
создайте форму вручную по следующему шаблону:

#### 🔹 Настройки формы:
- **Имя формы:** Журнал_выпуска_форма
- **Источник данных:** таблица ЖУРНАЛ_ВЫПУСКА
- **Тип:** Форма данных

#### 🔹 Поля формы:

**1. Дата выпуска**
- Тип: Поле даты
- Имя: ДАТА_ВЫПУСКА
- Связанное поле: ДАТА_ВЫПУСКА

**2. Выбор материала**
- Тип: Список (Combo Box)
- Имя: МАТЕРИАЛ_ID
- Связанное поле: МАТЕРИАЛ_ID
- Источник списка: SELECT ID, НАИМЕНОВАНИЕ FROM МАТЕРИАЛЫ ORDER BY НАИМЕНОВАНИЕ
- Связанный столбец: 0
- Отображаемый столбец: 1
- Событие "При изменении": AutoFillModule.OnMaterialChange

**3. Поиск по названию**
- Тип: Текстовое поле
- Имя: НАИМЕНОВАНИЕ_ПОИСК
- Связанное поле: (не связывать)
- Событие "При изменении": AutoFillModule.OnMaterialNameChange

**4. Инвентарный номер**
- Тип: Текстовое поле
- Имя: ИНВЕНТАРНЫЙ_НОМЕР
- Только для чтения: Да

**5. Количество**
- Тип: Числовое поле
- Имя: КОЛИЧЕСТВО
- Связанное поле: КОЛИЧЕСТВО
- Событие "При изменении": AutoFillModule.OnQuantityChange

**6. Трудозатраты на единицу**
- Тип: Числовое поле
- Имя: ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ
- Только для чтения: Да

**7. Общие трудозатраты**
- Тип: Числовое поле
- Имя: ОБЩИЕ_ТРУДОЗАТРАТЫ
- Связанное поле: ОБЩИЕ_ТРУДОЗАТРАТЫ
- Только для чтения: Да

**8. Примечания**
- Тип: Многострочное текстовое поле
- Имя: ПРИМЕЧАНИЯ
- Связанное поле: ПРИМЕЧАНИЯ

### Шаг 3: Проверка работы
1. Откройте форму
2. Выберите материал из списка
3. Проверьте автозаполнение полей
4. Введите количество и проверьте расчет трудозатрат

## ⚠️ ВАЖНО:
- Имена полей должны точно соответствовать указанным
- Макросы должны быть загружены и разрешены
- События должны быть правильно привязаны к макросам
"""
    
    return instructions

def main():
    """Основная функция"""
    
    print("🔧 АВТОМАТИЧЕСКАЯ НАСТРОЙКА ФОРМ LIBREOFFICE BASE")
    print("=" * 50)
    
    # Создание XML-шаблона формы
    print("📝 Создание XML-шаблона формы...")
    form_xml = create_form_template()
    
    # Сохранение XML-шаблона
    xml_filename = "форма_журнал_выпуска_шаблон.xml"
    tree = ET.ElementTree(form_xml)
    tree.write(xml_filename, encoding='utf-8', xml_declaration=True)
    print(f"✅ XML-шаблон сохранен: {xml_filename}")
    
    # Создание инструкций
    print("📋 Создание инструкций по настройке...")
    instructions = create_setup_instructions()
    
    instructions_filename = "ИНСТРУКЦИЯ_ПО_СОЗДАНИЮ_ФОРМ.md"
    with open(instructions_filename, 'w', encoding='utf-8') as f:
        f.write(instructions)
    print(f"✅ Инструкции сохранены: {instructions_filename}")
    
    print("\n🎉 ГОТОВО!")
    print("\n📋 Следующие шаги:")
    print("1. Откройте LibreOffice Base")
    print("2. Создайте форму вручную по инструкции")
    print("3. Настройте события и макросы")
    print("4. Протестируйте автозаполнение")
    
    print(f"\n📁 Созданные файлы:")
    print(f"   • {xml_filename}")
    print(f"   • {instructions_filename}")

if __name__ == "__main__":
    main()
