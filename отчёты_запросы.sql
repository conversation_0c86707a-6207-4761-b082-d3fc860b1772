-- СПЕЦИАЛЬНЫЕ ЗАПРОСЫ ДЛЯ ОТЧЁТОВ
-- Электронный журнал выпуска продукции

-- 1. ОСНОВНОЙ ОТЧЁТ ПО РАСХОДУ МАТЕРИАЛОВ ЗА МЕСЯЦ
-- Использование: укажите год и месяц в параметрах
CREATE VIEW ОТЧЁТ_МАТЕРИАЛЫ_МЕСЯЦ AS
SELECT 
    m.ИНВЕНТАРНЫЙ_НОМЕР as "Инвентарный номер",
    m.НАИМЕНОВАНИЕ as "Наименование материала", 
    m.ЕДИНИЦА_ИЗМЕРЕНИЯ as "Ед. изм.",
    SUM(j.КОЛИЧЕСТВО) as "Количество за месяц",
    SUM(j.ОБЩИЕ_ТРУДОЗАТРАТЫ) as "Общие трудозатраты",
    COUNT(j.ID) as "Количество операций",
    ROUND(AVG(j.КОЛИЧЕСТВО), 2) as "Среднее за операцию",
    MIN(j.ДАТА_ВЫПУСКА) as "Первое использование",
    MAX(j.ДАТА_ВЫПУСКА) as "Последнее использование"
FROM ЖУРНАЛ_ВЫПУСКА j 
JOIN МАТЕРИАЛЫ m ON j.МАТЕРИАЛ_ID = m.ID 
WHERE YEAR(j.ДАТА_ВЫПУСКА) = :ГОД AND MONTH(j.ДАТА_ВЫПУСКА) = :МЕСЯЦ 
GROUP BY m.ID, m.ИНВЕНТАРНЫЙ_НОМЕР, m.НАИМЕНОВАНИЕ, m.ЕДИНИЦА_ИЗМЕРЕНИЯ
ORDER BY SUM(j.КОЛИЧЕСТВО) DESC;

-- 2. ОТЧЁТ С ВОЗМОЖНОСТЬЮ ВЫБОРА ПЕРИОДА
-- Использование: укажите начальную и конечную даты
SELECT 
    j.ДАТА_ВЫПУСКА as "Дата",
    m.ИНВЕНТАРНЫЙ_НОМЕР as "Инв. номер",
    m.НАИМЕНОВАНИЕ as "Материал", 
    j.КОЛИЧЕСТВО as "Количество",
    m.ЕДИНИЦА_ИЗМЕРЕНИЯ as "Ед. изм.",
    j.ОБЩИЕ_ТРУДОЗАТРАТЫ as "Трудозатраты",
    j.ПРИМЕЧАНИЯ as "Примечания"
FROM ЖУРНАЛ_ВЫПУСКА j 
JOIN МАТЕРИАЛЫ m ON j.МАТЕРИАЛ_ID = m.ID 
WHERE j.ДАТА_ВЫПУСКА BETWEEN :ДАТА_НАЧАЛА AND :ДАТА_ОКОНЧАНИЯ
ORDER BY j.ДАТА_ВЫПУСКА DESC, m.НАИМЕНОВАНИЕ;

-- 3. СВОДНЫЙ ОТЧЁТ ПО МАТЕРИАЛАМ ЗА ПЕРИОД
SELECT 
    m.ИНВЕНТАРНЫЙ_НОМЕР as "Инвентарный номер",
    m.НАИМЕНОВАНИЕ as "Наименование материала", 
    m.ЕДИНИЦА_ИЗМЕРЕНИЯ as "Ед. изм.",
    SUM(j.КОЛИЧЕСТВО) as "Общее количество",
    SUM(j.ОБЩИЕ_ТРУДОЗАТРАТЫ) as "Общие трудозатраты",
    COUNT(j.ID) as "Операций",
    ROUND(SUM(j.КОЛИЧЕСТВО) / COUNT(j.ID), 2) as "Среднее за операцию",
    ROUND(SUM(j.ОБЩИЕ_ТРУДОЗАТРАТЫ) / SUM(j.КОЛИЧЕСТВО), 2) as "Трудозатраты на единицу"
FROM ЖУРНАЛ_ВЫПУСКА j 
JOIN МАТЕРИАЛЫ m ON j.МАТЕРИАЛ_ID = m.ID 
WHERE j.ДАТА_ВЫПУСКА BETWEEN :ДАТА_НАЧАЛА AND :ДАТА_ОКОНЧАНИЯ
GROUP BY m.ID, m.ИНВЕНТАРНЫЙ_НОМЕР, m.НАИМЕНОВАНИЕ, m.ЕДИНИЦА_ИЗМЕРЕНИЯ
ORDER BY SUM(j.КОЛИЧЕСТВО) DESC;

-- 4. ОТЧЁТ ПО МЕСЯЦАМ (для анализа динамики)
SELECT 
    YEAR(j.ДАТА_ВЫПУСКА) as "Год",
    MONTH(j.ДАТА_ВЫПУСКА) as "Месяц",
    m.ИНВЕНТАРНЫЙ_НОМЕР as "Инв. номер",
    m.НАИМЕНОВАНИЕ as "Материал",
    SUM(j.КОЛИЧЕСТВО) as "Количество",
    SUM(j.ОБЩИЕ_ТРУДОЗАТРАТЫ) as "Трудозатраты"
FROM ЖУРНАЛ_ВЫПУСКА j 
JOIN МАТЕРИАЛЫ m ON j.МАТЕРИАЛ_ID = m.ID 
WHERE j.ДАТА_ВЫПУСКА >= :ДАТА_НАЧАЛА
GROUP BY YEAR(j.ДАТА_ВЫПУСКА), MONTH(j.ДАТА_ВЫПУСКА), m.ID, m.ИНВЕНТАРНЫЙ_НОМЕР, m.НАИМЕНОВАНИЕ
ORDER BY YEAR(j.ДАТА_ВЫПУСКА) DESC, MONTH(j.ДАТА_ВЫПУСКА) DESC, SUM(j.КОЛИЧЕСТВО) DESC;

-- 5. ТОП-10 НАИБОЛЕЕ ИСПОЛЬЗУЕМЫХ МАТЕРИАЛОВ
SELECT TOP 10
    m.ИНВЕНТАРНЫЙ_НОМЕР as "Инвентарный номер",
    m.НАИМЕНОВАНИЕ as "Наименование материала",
    SUM(j.КОЛИЧЕСТВО) as "Общее количество",
    COUNT(j.ID) as "Количество операций",
    SUM(j.ОБЩИЕ_ТРУДОЗАТРАТЫ) as "Общие трудозатраты"
FROM ЖУРНАЛ_ВЫПУСКА j 
JOIN МАТЕРИАЛЫ m ON j.МАТЕРИАЛ_ID = m.ID 
WHERE j.ДАТА_ВЫПУСКА >= :ДАТА_НАЧАЛА
GROUP BY m.ID, m.ИНВЕНТАРНЫЙ_НОМЕР, m.НАИМЕНОВАНИЕ
ORDER BY SUM(j.КОЛИЧЕСТВО) DESC;

-- 6. ОТЧЁТ ПО ТРУДОЗАТРАТАМ
SELECT 
    m.ИНВЕНТАРНЫЙ_НОМЕР as "Инвентарный номер",
    m.НАИМЕНОВАНИЕ as "Наименование материала",
    m.ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ as "Трудозатраты за единицу",
    SUM(j.КОЛИЧЕСТВО) as "Количество",
    SUM(j.ОБЩИЕ_ТРУДОЗАТРАТЫ) as "Общие трудозатраты",
    ROUND(SUM(j.ОБЩИЕ_ТРУДОЗАТРАТЫ) * 100.0 / 
          (SELECT SUM(ОБЩИЕ_ТРУДОЗАТРАТЫ) FROM ЖУРНАЛ_ВЫПУСКА 
           WHERE ДАТА_ВЫПУСКА BETWEEN :ДАТА_НАЧАЛА AND :ДАТА_ОКОНЧАНИЯ), 2) as "% от общих трудозатрат"
FROM ЖУРНАЛ_ВЫПУСКА j 
JOIN МАТЕРИАЛЫ m ON j.МАТЕРИАЛ_ID = m.ID 
WHERE j.ДАТА_ВЫПУСКА BETWEEN :ДАТА_НАЧАЛА AND :ДАТА_ОКОНЧАНИЯ
GROUP BY m.ID, m.ИНВЕНТАРНЫЙ_НОМЕР, m.НАИМЕНОВАНИЕ, m.ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ
ORDER BY SUM(j.ОБЩИЕ_ТРУДОЗАТРАТЫ) DESC;

-- 7. БЫСТРЫЙ ПОИСК МАТЕРИАЛА ДЛЯ АВТОЗАПОЛНЕНИЯ
-- Используется в макросах для автозаполнения полей
SELECT 
    m.ID,
    m.ИНВЕНТАРНЫЙ_НОМЕР,
    m.ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ,
    m.ЕДИНИЦА_ИЗМЕРЕНИЯ
FROM МАТЕРИАЛЫ m
WHERE UPPER(m.НАИМЕНОВАНИЕ) LIKE UPPER('%' || :ПОИСК || '%')
ORDER BY 
    CASE 
        WHEN UPPER(m.НАИМЕНОВАНИЕ) = UPPER(:ПОИСК) THEN 1
        WHEN UPPER(m.НАИМЕНОВАНИЕ) LIKE UPPER(:ПОИСК || '%') THEN 2
        ELSE 3
    END,
    m.НАИМЕНОВАНИЕ
LIMIT 1;

-- 8. ПРОВЕРКА ДАННЫХ ЗА ТЕКУЩИЙ МЕСЯЦ
SELECT 
    COUNT(*) as "Записей за месяц",
    COUNT(DISTINCT j.МАТЕРИАЛ_ID) as "Различных материалов",
    SUM(j.КОЛИЧЕСТВО) as "Общее количество",
    SUM(j.ОБЩИЕ_ТРУДОЗАТРАТЫ) as "Общие трудозатраты",
    MIN(j.ДАТА_ВЫПУСКА) as "Первая запись",
    MAX(j.ДАТА_ВЫПУСКА) as "Последняя запись"
FROM ЖУРНАЛ_ВЫПУСКА j
WHERE YEAR(j.ДАТА_ВЫПУСКА) = YEAR(CURRENT_DATE) 
  AND MONTH(j.ДАТА_ВЫПУСКА) = MONTH(CURRENT_DATE);

-- 9. МАТЕРИАЛЫ БЕЗ ДВИЖЕНИЯ ЗА ПЕРИОД
SELECT 
    m.ИНВЕНТАРНЫЙ_НОМЕР as "Инвентарный номер",
    m.НАИМЕНОВАНИЕ as "Наименование материала",
    m.ЕДИНИЦА_ИЗМЕРЕНИЯ as "Ед. изм.",
    m.ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ as "Трудозатраты"
FROM МАТЕРИАЛЫ m
WHERE m.ID NOT IN (
    SELECT DISTINCT j.МАТЕРИАЛ_ID 
    FROM ЖУРНАЛ_ВЫПУСКА j 
    WHERE j.ДАТА_ВЫПУСКА BETWEEN :ДАТА_НАЧАЛА AND :ДАТА_ОКОНЧАНИЯ
)
ORDER BY m.НАИМЕНОВАНИЕ;

-- 10. ЭКСПОРТ ДАННЫХ ДЛЯ EXCEL (все поля)
SELECT 
    j.ДАТА_ВЫПУСКА as "Дата выпуска",
    m.ИНВЕНТАРНЫЙ_НОМЕР as "Инвентарный номер",
    m.НАИМЕНОВАНИЕ as "Наименование материала",
    m.ЕДИНИЦА_ИЗМЕРЕНИЯ as "Единица измерения",
    j.КОЛИЧЕСТВО as "Количество",
    m.ТРУДОЗАТРАТЫ_НА_ЕДИНИЦУ as "Трудозатраты на единицу",
    j.ОБЩИЕ_ТРУДОЗАТРАТЫ as "Общие трудозатраты",
    j.ПРИМЕЧАНИЯ as "Примечания",
    j.ДАТА_СОЗДАНИЯ as "Дата создания записи"
FROM ЖУРНАЛ_ВЫПУСКА j 
JOIN МАТЕРИАЛЫ m ON j.МАТЕРИАЛ_ID = m.ID 
WHERE j.ДАТА_ВЫПУСКА BETWEEN :ДАТА_НАЧАЛА AND :ДАТА_ОКОНЧАНИЯ
ORDER BY j.ДАТА_ВЫПУСКА DESC, m.НАИМЕНОВАНИЕ;